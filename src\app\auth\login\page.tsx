'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { Phone, Mail, ArrowLeft } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/components/ui/Toaster';
import { LoadingButton } from '@/components/ui/LoadingSpinner';
import { RedirectMessage } from '@/components/auth/RedirectMessage';
import { authApi } from '@/lib/api';
import { LoginResponse } from '@/types/api';
import { isUnregisteredMobileError, isInvalidOTPError, getUserFriendlyErrorMessage } from '@/lib/errorUtils';

export default function LoginPage() {
  const [loginMethod, setLoginMethod] = useState<'mobile' | 'email'>('mobile');
  const [formData, setFormData] = useState({
    mobile_number: '',
    email: '',
    password: '',
    otp: '',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [otpSent, setOtpSent] = useState(false);
  const [otpTimer, setOtpTimer] = useState(0);

  const { login } = useAuth();
  const { showToast } = useToast();
  const router = useRouter();
  const searchParams = useSearchParams();
  const redirectTo = searchParams.get('redirect') || '/';

  // Pre-fill mobile number from URL parameter if available
  useEffect(() => {
    const mobileFromUrl = searchParams.get('mobile');
    if (mobileFromUrl) {
      setFormData(prev => ({ ...prev, mobile_number: mobileFromUrl }));
    }
  }, [searchParams]);

  // Start OTP timer
  const startOtpTimer = () => {
    setOtpTimer(60);
    const interval = setInterval(() => {
      setOtpTimer((prev) => {
        if (prev <= 1) {
          clearInterval(interval);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  const handleSendOTP = async () => {
    if (!formData.mobile_number) {
      showToast({ type: 'error', title: 'Please enter mobile number' });
      return;
    }

    setIsLoading(true);
    try {
      await authApi.sendOTP(formData.mobile_number);
      setOtpSent(true);
      startOtpTimer();
      showToast({ type: 'success', title: 'OTP sent successfully' });
    } catch (error: any) {
      // Check if the error is due to unregistered mobile number
      if (isUnregisteredMobileError(error)) {
        showToast({
          type: 'error',
          title: 'Mobile number not registered',
          message: 'This mobile number is not registered. Redirecting to signup page...'
        });
        // Redirect to registration page after a short delay
        setTimeout(() => {
          router.push(`/auth/register?mobile=${encodeURIComponent(formData.mobile_number)}&from=login`);
        }, 2000);
      } else {
        showToast({ type: 'error', title: 'Failed to send OTP', message: getUserFriendlyErrorMessage(error) });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendOTP = async () => {
    setIsLoading(true);
    try {
      await authApi.sendOTP(formData.mobile_number);
      startOtpTimer();
      showToast({ type: 'success', title: 'OTP resent successfully' });
    } catch (error: any) {
      // Check if the error is due to unregistered mobile number
      if (isUnregisteredMobileError(error)) {
        showToast({
          type: 'error',
          title: 'Mobile number not registered',
          message: 'This mobile number is not registered. Redirecting to signup page...'
        });
        // Redirect to registration page after a short delay
        setTimeout(() => {
          router.push(`/auth/register?mobile=${encodeURIComponent(formData.mobile_number)}&from=login`);
        }, 2000);
      } else {
        showToast({ type: 'error', title: 'Failed to resend OTP', message: getUserFriendlyErrorMessage(error) });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleMobileLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.mobile_number || !formData.otp) {
      showToast({ type: 'error', title: 'Please enter mobile number and OTP' });
      return;
    }

    setIsLoading(true);
    try {
      const response = await authApi.loginMobile(formData.mobile_number, formData.otp);
      login(response as LoginResponse);
      showToast({ type: 'success', title: 'Login successful' });
      router.push(redirectTo);
    } catch (error: any) {
      if (isInvalidOTPError(error)) {
        showToast({ type: 'error', title: 'Invalid OTP', message: getUserFriendlyErrorMessage(error) });
      } else {
        showToast({ type: 'error', title: 'Login failed', message: getUserFriendlyErrorMessage(error) });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleEmailLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.email || !formData.password) {
      showToast({ type: 'error', title: 'Please enter email and password' });
      return;
    }

    setIsLoading(true);
    try {
      const response = await authApi.loginEmail(formData.email, formData.password);
      login(response as LoginResponse);
      showToast({ type: 'success', title: 'Login successful' });
      router.push(redirectTo);
    } catch (error: any) {
      if (isInvalidOTPError(error)) {
        showToast({ type: 'error', title: 'Invalid OTP', message: getUserFriendlyErrorMessage(error) });
      } else {
        showToast({ type: 'error', title: 'Login failed', message: getUserFriendlyErrorMessage(error) });
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <Link href="/" className="flex items-center justify-center mb-6">
          <ArrowLeft className="h-5 w-5 mr-2" />
          Back to Home
        </Link>
        
        <div className="flex justify-center mb-6">
          <div className="w-12 h-12 bg-primary-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-lg">HS</span>
          </div>
        </div>
        
        <h2 className="text-center text-3xl font-bold text-gray-900">
          Sign in to your account
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          Or{' '}
          <Link href="/auth/register" className="font-medium text-primary-600 hover:text-primary-500">
            create a new account
          </Link>
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <RedirectMessage type="login" />
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          {/* Login Method Toggle */}
          <div className="flex rounded-lg bg-gray-100 p-1 mb-6">
            <button
              onClick={() => setLoginMethod('mobile')}
              className={`flex-1 flex items-center justify-center py-2 px-3 rounded-md text-sm font-medium transition-colors ${
                loginMethod === 'mobile'
                  ? 'bg-white text-primary-600 shadow-sm'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              <Phone className="h-4 w-4 mr-2" />
              Mobile
            </button>
            <button
              onClick={() => setLoginMethod('email')}
              className={`flex-1 flex items-center justify-center py-2 px-3 rounded-md text-sm font-medium transition-colors ${
                loginMethod === 'email'
                  ? 'bg-white text-primary-600 shadow-sm'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              <Mail className="h-4 w-4 mr-2" />
              Email
            </button>
          </div>

          {/* Mobile Login Form */}
          {loginMethod === 'mobile' && (
            <form onSubmit={handleMobileLogin} className="space-y-6">
              <div>
                <label htmlFor="mobile" className="block text-sm font-medium text-gray-700">
                  Mobile Number
                </label>
                <div className="mt-1">
                  <input
                    id="mobile"
                    name="mobile"
                    type="tel"
                    required
                    value={formData.mobile_number}
                    onChange={(e) => setFormData({ ...formData, mobile_number: e.target.value })}
                    placeholder="+91 98765 43210"
                    className="input"
                  />
                </div>
              </div>

              {!otpSent ? (
                <LoadingButton
                  type="button"
                  onClick={handleSendOTP}
                  isLoading={isLoading}
                  className="w-full btn-primary"
                >
                  Send OTP
                </LoadingButton>
              ) : (
                <>
                  <div>
                    <label htmlFor="otp" className="block text-sm font-medium text-gray-700">
                      Enter OTP
                    </label>
                    <div className="mt-1">
                      <input
                        id="otp"
                        name="otp"
                        type="text"
                        required
                        value={formData.otp}
                        onChange={(e) => setFormData({ ...formData, otp: e.target.value })}
                        placeholder="123456"
                        className="input"
                        maxLength={6}
                      />
                    </div>
                  </div>

                  <div className="flex space-x-3">
                    <LoadingButton
                      type="submit"
                      isLoading={isLoading}
                      className="flex-1 btn-primary"
                    >
                      Verify & Login
                    </LoadingButton>
                    
                    {otpTimer === 0 ? (
                      <LoadingButton
                        type="button"
                        onClick={handleResendOTP}
                        isLoading={isLoading}
                        className="btn-outline"
                      >
                        Resend
                      </LoadingButton>
                    ) : (
                      <button
                        type="button"
                        disabled
                        className="btn-outline opacity-50 cursor-not-allowed"
                      >
                        {otpTimer}s
                      </button>
                    )}
                  </div>
                </>
              )}
            </form>
          )}

          {/* Email Login Form */}
          {loginMethod === 'email' && (
            <form onSubmit={handleEmailLogin} className="space-y-6">
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                  Email address
                </label>
                <div className="mt-1">
                  <input
                    id="email"
                    name="email"
                    type="email"
                    autoComplete="email"
                    required
                    value={formData.email}
                    onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                    className="input"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                  Password
                </label>
                <div className="mt-1">
                  <input
                    id="password"
                    name="password"
                    type="password"
                    autoComplete="current-password"
                    required
                    value={formData.password}
                    onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                    className="input"
                  />
                </div>
              </div>

              <LoadingButton
                type="submit"
                isLoading={isLoading}
                className="w-full btn-primary"
              >
                Sign in
              </LoadingButton>
            </form>
          )}
        </div>
      </div>
    </div>
  );
}
