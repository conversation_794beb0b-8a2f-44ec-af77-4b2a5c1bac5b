'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import { Filter, ShoppingCart, Clock, Star, Grid, List } from 'lucide-react';
import { MainLayout } from '@/components/layout/MainLayout';
import { LoadingPage, LoadingButton } from '@/components/ui/LoadingSpinner';
import { useCart } from '@/contexts/CartContext';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/components/ui/Toaster';
import { catalogueApi } from '@/lib/api';
import { Service, Category } from '@/types/api';

export default function ServicesPage() {
  const [services, setServices] = useState<Service[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [addingToCart, setAddingToCart] = useState<number | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [sortBy, setSortBy] = useState('title');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = useState(false);

  const router = useRouter();
  const { addToCart } = useCart();
  const { isAuthenticated } = useAuth();
  const { showToast } = useToast();

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    setIsLoading(true);
    try {
      const [servicesData, categoriesData] = await Promise.all([
        catalogueApi.getServices({ ordering: sortBy }),
        catalogueApi.getCategories({ level: 0 }),
      ]);

      console.log('Services data:', servicesData);
      console.log('Categories data:', categoriesData);

      // Handle services data
      if (Array.isArray(servicesData)) {
        setServices(servicesData as Service[]);
      } else {
        console.warn('Services data is not an array:', servicesData);
        setServices([]);
      }

      // Handle categories data
      if (Array.isArray(categoriesData)) {
        setCategories(categoriesData as Category[]);
      } else {
        console.warn('Categories data is not an array:', categoriesData);
        setCategories([]);
      }
    } catch (error) {
      console.error('Failed to fetch services:', error);
      showToast({ type: 'error', title: 'Failed to load services' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleFilter = async () => {
    setIsLoading(true);
    try {
      const params: any = { ordering: sortBy };
      if (selectedCategory) params.category = selectedCategory;

      const servicesData = await catalogueApi.getServices(params);
      setServices(servicesData as Service[]);
    } catch (error) {
      console.error('Failed to apply filters:', error);
      showToast({ type: 'error', title: 'Failed to apply filters' });
    } finally {
      setIsLoading(false);
    }
  };

  const clearFilters = () => {
    setSelectedCategory('');
    setSortBy('title');
    fetchData();
  };

  const handleAddToCart = async (service: Service) => {
    if (!isAuthenticated) {
      router.push(`/auth/login?redirect=${encodeURIComponent(window.location.pathname)}`);
      return;
    }

    setAddingToCart(service.id);
    try {
      await addToCart(service);
      showToast({ type: 'success', title: 'Added to cart', message: service.title });
    } catch (error: any) {
      showToast({ type: 'error', title: 'Failed to add to cart', message: error.message });
    } finally {
      setAddingToCart(null);
    }
  };

  if (isLoading) {
    return (
      <MainLayout>
        <LoadingPage message="Loading services..." />
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">All Services</h1>
          <p className="text-gray-600">
            Discover our complete range of professional home services
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Filters Sidebar */}
          <div className="lg:col-span-1">
            <div className="card p-6 sticky top-24">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Filters</h3>
                <button
                  onClick={() => setShowFilters(!showFilters)}
                  className="lg:hidden btn-outline"
                >
                  <Filter className="h-4 w-4" />
                </button>
              </div>

              <div className={`space-y-6 ${showFilters ? 'block' : 'hidden lg:block'}`}>
                {/* Category Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Category
                  </label>
                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="input"
                  >
                    <option value="">All Categories</option>
                    {categories && Array.isArray(categories) && categories.map((category) => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Sort By */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Sort By
                  </label>
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="input"
                  >
                    <option value="title">Name (A-Z)</option>
                    <option value="-title">Name (Z-A)</option>
                    <option value="base_price">Price (Low to High)</option>
                    <option value="-base_price">Price (High to Low)</option>
                    <option value="-created_at">Newest First</option>
                    <option value="created_at">Oldest First</option>
                  </select>
                </div>

                {/* Filter Actions */}
                <div className="space-y-2">
                  <button
                    onClick={handleFilter}
                    className="w-full btn-primary"
                  >
                    Apply Filters
                  </button>
                  <button
                    onClick={clearFilters}
                    className="w-full btn-secondary"
                  >
                    Clear Filters
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Services List */}
          <div className="lg:col-span-3">
            {/* View Controls */}
            <div className="flex items-center justify-between mb-6">
              <p className="text-gray-600">
                {services.length} service{services.length !== 1 ? 's' : ''} found
              </p>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 rounded ${
                    viewMode === 'grid' 
                      ? 'bg-primary-100 text-primary-600' 
                      : 'text-gray-400 hover:text-gray-600'
                  }`}
                >
                  <Grid className="h-4 w-4" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 rounded ${
                    viewMode === 'list' 
                      ? 'bg-primary-100 text-primary-600' 
                      : 'text-gray-400 hover:text-gray-600'
                  }`}
                >
                  <List className="h-4 w-4" />
                </button>
              </div>
            </div>

            {services.length > 0 ? (
              <div className={
                viewMode === 'grid' 
                  ? 'grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6'
                  : 'space-y-6'
              }>
                {services.map((service) => (
                  <div 
                    key={service.id} 
                    className={`card-hover overflow-hidden ${
                      viewMode === 'list' ? 'flex' : ''
                    }`}
                  >
                    {/* Service Image */}
                    <div className={`bg-gray-200 relative ${
                      viewMode === 'list' ? 'w-48 h-32 flex-shrink-0' : 'h-48'
                    }`}>
                      {service.image ? (
                        <Image
                          src={service.image}
                          alt={service.title}
                          fill
                          className="object-cover"
                        />
                      ) : (
                        <div className="w-full h-full bg-primary-100 flex items-center justify-center">
                          <span className="text-primary-600 font-bold text-2xl">
                            {service.title.charAt(0)}
                          </span>
                        </div>
                      )}
                      {service.discount_percentage && service.discount_percentage > 0 && (
                        <div className="absolute top-2 right-2 bg-red-500 text-white px-2 py-1 rounded text-sm font-medium">
                          {service.discount_percentage}% OFF
                        </div>
                      )}
                    </div>

                    {/* Service Details */}
                    <div className="p-6 flex-1">
                      <div className="mb-2">
                        <span className="text-sm text-primary-600 font-medium">
                          {service.category_name}
                        </span>
                      </div>
                      
                      <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                        {service.title}
                      </h3>
                      
                      <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                        {service.description}
                      </p>

                      {/* Service Info */}
                      <div className="flex items-center space-x-4 mb-4 text-sm text-gray-500">
                        <div className="flex items-center">
                          <Clock className="h-4 w-4 mr-1" />
                          {service.time_to_complete}
                        </div>
                        <div className="flex items-center">
                          <Star className="h-4 w-4 mr-1 text-yellow-400" />
                          4.8
                        </div>
                      </div>

                      {/* Pricing */}
                      <div className="flex items-center justify-between mb-4">
                        <div>
                          <div className="flex items-center space-x-2">
                            <span className="text-2xl font-bold text-gray-900">
                              ₹{service.current_price}
                            </span>
                            {service.discount_price && service.base_price !== service.current_price && (
                              <span className="text-lg text-gray-500 line-through">
                                ₹{service.base_price}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Actions */}
                      <div className={`flex space-x-2 ${viewMode === 'list' ? 'flex-col' : ''}`}>
                        <Link
                          href={`/services/${service.slug}`}
                          className="flex-1 btn-outline text-center"
                        >
                          View Details
                        </Link>
                        <LoadingButton
                          onClick={() => handleAddToCart(service)}
                          isLoading={addingToCart === service.id}
                          className="flex-1 btn-primary"
                        >
                          <ShoppingCart className="h-4 w-4 mr-2" />
                          Add to Cart
                        </LoadingButton>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-16">
                <div className="w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-6">
                  <span className="text-gray-400 text-2xl">🔧</span>
                </div>
                <h2 className="text-2xl font-bold text-gray-900 mb-4">No services found</h2>
                <p className="text-gray-600 mb-8">
                  Try adjusting your filters or browse our categories.
                </p>
                <Link href="/" className="btn-primary">
                  Browse Categories
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
