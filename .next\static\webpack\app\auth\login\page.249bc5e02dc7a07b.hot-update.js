"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/login/page",{

/***/ "(app-pages-browser)/./src/lib/errorUtils.ts":
/*!*******************************!*\
  !*** ./src/lib/errorUtils.ts ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getUserFriendlyErrorMessage: function() { return /* binding */ getUserFriendlyErrorMessage; },\n/* harmony export */   isAlreadyRegisteredError: function() { return /* binding */ isAlreadyRegisteredError; },\n/* harmony export */   isInvalidOTPError: function() { return /* binding */ isInvalidOTPError; },\n/* harmony export */   isRateLimitError: function() { return /* binding */ isRateLimitError; },\n/* harmony export */   isUnregisteredMobileError: function() { return /* binding */ isUnregisteredMobileError; }\n/* harmony export */ });\n/**\n * Check if an API error indicates an unregistered mobile number\n */ const isUnregisteredMobileError = (error)=>{\n    var _error_message, _error_details, _error_details1;\n    if (error.status !== 400) return false;\n    const message = ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.toLowerCase()) || \"\";\n    const detailMessage = ((_error_details = error.details) === null || _error_details === void 0 ? void 0 : _error_details.detail) ? (Array.isArray(error.details.detail) ? error.details.detail.join(\" \") : String(error.details.detail)).toLowerCase() : \"\";\n    const errorMessage = ((_error_details1 = error.details) === null || _error_details1 === void 0 ? void 0 : _error_details1.error) ? (Array.isArray(error.details.error) ? error.details.error.join(\" \") : String(error.details.error)).toLowerCase() : \"\";\n    const unregisteredKeywords = [\n        \"not registered\",\n        \"unregistered\",\n        \"mobile number not found\",\n        \"user not found\",\n        \"does not exist\",\n        \"no account found\",\n        \"please register\"\n    ];\n    return unregisteredKeywords.some((keyword)=>message.includes(keyword) || detailMessage.includes(keyword) || errorMessage.includes(keyword));\n};\n/**\n * Check if an API error indicates an already registered mobile number\n */ const isAlreadyRegisteredError = (error)=>{\n    var _error_message, _error_details, _error_details1;\n    if (error.status !== 400) return false;\n    const message = ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.toLowerCase()) || \"\";\n    const detailMessage = ((_error_details = error.details) === null || _error_details === void 0 ? void 0 : _error_details.detail) ? (Array.isArray(error.details.detail) ? error.details.detail.join(\" \") : String(error.details.detail)).toLowerCase() : \"\";\n    const errorMessage = ((_error_details1 = error.details) === null || _error_details1 === void 0 ? void 0 : _error_details1.error) ? (Array.isArray(error.details.error) ? error.details.error.join(\" \") : String(error.details.error)).toLowerCase() : \"\";\n    const registeredKeywords = [\n        \"already registered\",\n        \"already exists\",\n        \"mobile number already in use\",\n        \"user already exists\",\n        \"duplicate\"\n    ];\n    return registeredKeywords.some((keyword)=>message.includes(keyword) || detailMessage.includes(keyword) || errorMessage.includes(keyword));\n};\n/**\n * Check if an API error indicates invalid OTP\n */ const isInvalidOTPError = (error)=>{\n    var _error_message, _error_details, _error_details1;\n    if (error.status !== 400) return false;\n    const message = ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.toLowerCase()) || \"\";\n    const detailMessage = ((_error_details = error.details) === null || _error_details === void 0 ? void 0 : _error_details.detail) ? (Array.isArray(error.details.detail) ? error.details.detail.join(\" \") : String(error.details.detail)).toLowerCase() : \"\";\n    const errorMessage = ((_error_details1 = error.details) === null || _error_details1 === void 0 ? void 0 : _error_details1.error) ? (Array.isArray(error.details.error) ? error.details.error.join(\" \") : String(error.details.error)).toLowerCase() : \"\";\n    const otpKeywords = [\n        \"invalid otp\",\n        \"incorrect otp\",\n        \"otp expired\",\n        \"otp not found\",\n        \"wrong otp\"\n    ];\n    return otpKeywords.some((keyword)=>message.includes(keyword) || detailMessage.includes(keyword) || errorMessage.includes(keyword));\n};\n/**\n * Check if the error is due to rate limiting (too many requests)\n */ const isRateLimitError = (error)=>{\n    var _error_details, _error_details1;\n    // Check for 429 status code\n    if (error.status === 429) {\n        return true;\n    }\n    const message = error.message ? error.message.toLowerCase() : \"\";\n    const detailMessage = ((_error_details = error.details) === null || _error_details === void 0 ? void 0 : _error_details.detail) ? (Array.isArray(error.details.detail) ? error.details.detail.join(\" \") : String(error.details.detail)).toLowerCase() : \"\";\n    const errorMessage = ((_error_details1 = error.details) === null || _error_details1 === void 0 ? void 0 : _error_details1.error) ? (Array.isArray(error.details.error) ? error.details.error.join(\" \") : String(error.details.error)).toLowerCase() : \"\";\n    const rateLimitKeywords = [\n        \"too many requests\",\n        \"too many otp requests\",\n        \"rate limit\",\n        \"try again later\",\n        \"please wait\",\n        \"request limit exceeded\",\n        \"rate exceeded\"\n    ];\n    return rateLimitKeywords.some((keyword)=>message.includes(keyword) || detailMessage.includes(keyword) || errorMessage.includes(keyword));\n};\n/**\n * Get a user-friendly error message from an API error\n */ const getUserFriendlyErrorMessage = (error)=>{\n    if (isUnregisteredMobileError(error)) {\n        return \"This mobile number is not registered. You will be redirected to create an account.\";\n    }\n    if (isAlreadyRegisteredError(error)) {\n        return \"This mobile number is already registered. Please login instead.\";\n    }\n    if (isInvalidOTPError(error)) {\n        return \"Invalid or expired OTP. Please try again.\";\n    }\n    if (isRateLimitError(error)) {\n        return \"Too many OTP requests. Please wait for some time and try again.\";\n    }\n    // Return the original error message or a generic one\n    return error.message || \"An unexpected error occurred. Please try again.\";\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/errorUtils.ts\n"));

/***/ })

});