if(!self.define){let e,s={};const c=(c,t)=>(c=new URL(c+".js",t).href,s[c]||new Promise((s=>{if("document"in self){const e=document.createElement("script");e.src=c,e.onload=s,document.head.appendChild(e)}else e=c,importScripts(c),s()})).then((()=>{let e=s[c];if(!e)throw new Error(`Module ${c} didn’t register its module`);return e})));self.define=(t,a)=>{const n=e||("document"in self?document.currentScript.src:"")||location.href;if(s[n])return;let i={};const r=e=>c(e,n),o={module:{uri:n},exports:i,require:r};s[n]=Promise.all(t.map((e=>o[e]||r(e)))).then((e=>(a(...e),i)))}}define(["./workbox-4754cb34"],(function(e){"use strict";importScripts(),self.skipWaiting(),e.clientsClaim(),e.precacheAndRoute([{url:"/_next/app-build-manifest.json",revision:"edddafec693bb5487ced7d73b254b95a"},{url:"/_next/static/0cyx4kNtb8eNcSXgZQL2B/_buildManifest.js",revision:"41a968fdf4a5a66f7c385d388f55a779"},{url:"/_next/static/0cyx4kNtb8eNcSXgZQL2B/_ssgManifest.js",revision:"b6652df95db52feb4daf4eca35380933"},{url:"/_next/static/chunks/159-ca77c59390cfe62d.js",revision:"0cyx4kNtb8eNcSXgZQL2B"},{url:"/_next/static/chunks/221-7cc18aa9a8a91fdc.js",revision:"0cyx4kNtb8eNcSXgZQL2B"},{url:"/_next/static/chunks/224-31279b99a6e91423.js",revision:"0cyx4kNtb8eNcSXgZQL2B"},{url:"/_next/static/chunks/250-25f91bc4f08a4813.js",revision:"0cyx4kNtb8eNcSXgZQL2B"},{url:"/_next/static/chunks/652-67232859384e2bd6.js",revision:"0cyx4kNtb8eNcSXgZQL2B"},{url:"/_next/static/chunks/938-ee36b187d0dcebb1.js",revision:"0cyx4kNtb8eNcSXgZQL2B"},{url:"/_next/static/chunks/995-a688179e082778df.js",revision:"0cyx4kNtb8eNcSXgZQL2B"},{url:"/_next/static/chunks/app/addresses/page-1db38d6d7a817f57.js",revision:"0cyx4kNtb8eNcSXgZQL2B"},{url:"/_next/static/chunks/app/auth/login/page-c51a0810e2084fd8.js",revision:"0cyx4kNtb8eNcSXgZQL2B"},{url:"/_next/static/chunks/app/auth/register/page-31d4317c86a9a6b2.js",revision:"0cyx4kNtb8eNcSXgZQL2B"},{url:"/_next/static/chunks/app/cart/page-3067e535c4abe5ed.js",revision:"0cyx4kNtb8eNcSXgZQL2B"},{url:"/_next/static/chunks/app/categories/%5Bslug%5D/page-65b4c1e56892bf5c.js",revision:"0cyx4kNtb8eNcSXgZQL2B"},{url:"/_next/static/chunks/app/checkout/address/page-c7f92eebd1e699f7.js",revision:"0cyx4kNtb8eNcSXgZQL2B"},{url:"/_next/static/chunks/app/checkout/payment/page-9a99b7fcf8477a0a.js",revision:"0cyx4kNtb8eNcSXgZQL2B"},{url:"/_next/static/chunks/app/checkout/schedule/page-1b1689f4259fae0c.js",revision:"0cyx4kNtb8eNcSXgZQL2B"},{url:"/_next/static/chunks/app/contact/page-ca8a7807ff4a8d1e.js",revision:"0cyx4kNtb8eNcSXgZQL2B"},{url:"/_next/static/chunks/app/error-a729b935379cac07.js",revision:"0cyx4kNtb8eNcSXgZQL2B"},{url:"/_next/static/chunks/app/layout-2ad9d891083b9afc.js",revision:"0cyx4kNtb8eNcSXgZQL2B"},{url:"/_next/static/chunks/app/loading-37f974aef2a5829b.js",revision:"0cyx4kNtb8eNcSXgZQL2B"},{url:"/_next/static/chunks/app/not-found-a9caf70dab008aee.js",revision:"0cyx4kNtb8eNcSXgZQL2B"},{url:"/_next/static/chunks/app/orders/%5BorderNumber%5D/page-d399d31a41f59b24.js",revision:"0cyx4kNtb8eNcSXgZQL2B"},{url:"/_next/static/chunks/app/orders/page-1243d8d671781d5c.js",revision:"0cyx4kNtb8eNcSXgZQL2B"},{url:"/_next/static/chunks/app/page-31793ab1da5ea21b.js",revision:"0cyx4kNtb8eNcSXgZQL2B"},{url:"/_next/static/chunks/app/profile/page-1c85a3a36882e4d4.js",revision:"0cyx4kNtb8eNcSXgZQL2B"},{url:"/_next/static/chunks/app/search/page-99e54253a2cb442a.js",revision:"0cyx4kNtb8eNcSXgZQL2B"},{url:"/_next/static/chunks/app/services/%5Bslug%5D/page-f1c055ef07d10e6c.js",revision:"0cyx4kNtb8eNcSXgZQL2B"},{url:"/_next/static/chunks/app/services/page-fbc43d88a7508505.js",revision:"0cyx4kNtb8eNcSXgZQL2B"},{url:"/_next/static/chunks/fd9d1056-bee54734699614b4.js",revision:"0cyx4kNtb8eNcSXgZQL2B"},{url:"/_next/static/chunks/framework-c5181c9431ddc45b.js",revision:"0cyx4kNtb8eNcSXgZQL2B"},{url:"/_next/static/chunks/main-4d19b7575b31082e.js",revision:"0cyx4kNtb8eNcSXgZQL2B"},{url:"/_next/static/chunks/main-app-f93f729d78eedab7.js",revision:"0cyx4kNtb8eNcSXgZQL2B"},{url:"/_next/static/chunks/pages/_app-98cb51ec6f9f135f.js",revision:"0cyx4kNtb8eNcSXgZQL2B"},{url:"/_next/static/chunks/pages/_error-9157e57c362a0d0d.js",revision:"0cyx4kNtb8eNcSXgZQL2B"},{url:"/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js",revision:"837c0df77fd5009c9e46d446188ecfd0"},{url:"/_next/static/chunks/webpack-dbd4d8648dbed36c.js",revision:"0cyx4kNtb8eNcSXgZQL2B"},{url:"/_next/static/css/0c2829a04414ff39.css",revision:"0c2829a04414ff39"},{url:"/_next/static/media/26a46d62cd723877-s.woff2",revision:"befd9c0fdfa3d8a645d5f95717ed6420"},{url:"/_next/static/media/55c55f0601d81cf3-s.woff2",revision:"43828e14271c77b87e3ed582dbff9f74"},{url:"/_next/static/media/581909926a08bbc8-s.woff2",revision:"f0b86e7c24f455280b8df606b89af891"},{url:"/_next/static/media/8e9860b6e62d6359-s.woff2",revision:"01ba6c2a184b8cba08b0d57167664d75"},{url:"/_next/static/media/97e0cb1ae144a2a9-s.woff2",revision:"e360c61c5bd8d90639fd4503c829c2dc"},{url:"/_next/static/media/df0a9ae256c0569c-s.woff2",revision:"d54db44de5ccb18886ece2fda72bdfe0"},{url:"/_next/static/media/e4af272ccee01ff0-s.p.woff2",revision:"65850a373e258f1c897a2b3d75eb74de"},{url:"/favicon.ico",revision:"e4d13187ccd69d9840e28588621cfa79"},{url:"/icons/README.md",revision:"c3f5f9239b745d0aa5711455ba5ad4f3"},{url:"/manifest.json",revision:"36aa0439ce6369286ee9692504c8c24b"}],{ignoreURLParametersMatching:[]}),e.cleanupOutdatedCaches(),e.registerRoute("/",new e.NetworkFirst({cacheName:"start-url",plugins:[{cacheWillUpdate:async({request:e,response:s,event:c,state:t})=>s&&"opaqueredirect"===s.type?new Response(s.body,{status:200,statusText:"OK",headers:s.headers}):s}]}),"GET"),e.registerRoute(/^https:\/\/fonts\.(?:gstatic)\.com\/.*/i,new e.CacheFirst({cacheName:"google-fonts-webfonts",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:31536e3})]}),"GET"),e.registerRoute(/^https:\/\/fonts\.(?:googleapis)\.com\/.*/i,new e.StaleWhileRevalidate({cacheName:"google-fonts-stylesheets",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800})]}),"GET"),e.registerRoute(/\.(?:eot|otf|ttc|ttf|woff|woff2|font.css)$/i,new e.StaleWhileRevalidate({cacheName:"static-font-assets",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800})]}),"GET"),e.registerRoute(/\.(?:jpg|jpeg|gif|png|svg|ico|webp)$/i,new e.StaleWhileRevalidate({cacheName:"static-image-assets",plugins:[new e.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\/_next\/image\?url=.+$/i,new e.StaleWhileRevalidate({cacheName:"next-image",plugins:[new e.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:mp3|wav|ogg)$/i,new e.CacheFirst({cacheName:"static-audio-assets",plugins:[new e.RangeRequestsPlugin,new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:mp4)$/i,new e.CacheFirst({cacheName:"static-video-assets",plugins:[new e.RangeRequestsPlugin,new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:js)$/i,new e.StaleWhileRevalidate({cacheName:"static-js-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:css|less)$/i,new e.StaleWhileRevalidate({cacheName:"static-style-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\/_next\/data\/.+\/.+\.json$/i,new e.StaleWhileRevalidate({cacheName:"next-data",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:json|xml|csv)$/i,new e.NetworkFirst({cacheName:"static-data-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute((({url:e})=>{if(!(self.origin===e.origin))return!1;const s=e.pathname;return!s.startsWith("/api/auth/")&&!!s.startsWith("/api/")}),new e.NetworkFirst({cacheName:"apis",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:16,maxAgeSeconds:86400})]}),"GET"),e.registerRoute((({url:e})=>{if(!(self.origin===e.origin))return!1;return!e.pathname.startsWith("/api/")}),new e.NetworkFirst({cacheName:"others",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute((({url:e})=>!(self.origin===e.origin)),new e.NetworkFirst({cacheName:"cross-origin",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:3600})]}),"GET")}));
