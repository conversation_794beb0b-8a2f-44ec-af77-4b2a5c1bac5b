# CORS Issue Fix

## Problem
The Next.js frontend (localhost:3000) was unable to fetch data from the Django backend (localhost:8000) due to CORS (Cross-Origin Resource Sharing) restrictions.

## Root Cause
The Django backend was not configured to allow cross-origin requests from the frontend domain.

## Temporary Solution (Currently Implemented)
Created Next.js API proxy routes that forward requests to the Django backend:
- `/src/app/api/catalogue/categories/route.ts` - Proxies category requests
- `/src/app/api/catalogue/services/route.ts` - Proxies service requests

The frontend now calls these proxy routes instead of calling the Django backend directly.

## Permanent Solution (Recommended)
Configure CORS in your Django backend by following these steps:

### 1. Install django-cors-headers
```bash
pip install django-cors-headers
```

### 2. Update Django settings.py
```python
INSTALLED_APPS = [
    # ... other apps
    'corsheaders',
    # ... rest of your apps
]

MIDDLEWARE = [
    # ... other middleware
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    # ... rest of your middleware
]

# CORS settings
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
]

# For development only - allows all origins
CORS_ALLOW_ALL_ORIGINS = True

# Allow credentials
CORS_ALLOW_CREDENTIALS = True

# Allow specific headers
CORS_ALLOW_HEADERS = [
    'accept',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
]
```

### 3. Restart Django server
After making these changes, restart your Django development server.

### 4. Revert frontend changes
Once CORS is properly configured in Django, you can revert the API calls in `src/lib/api.ts` to call the Django backend directly:

```typescript
// Change from:
return apiRequest(`/api/catalogue/categories${query}`);

// Back to:
return apiRequest(`/catalogue/categories/${query}`);
```

And remove the proxy API routes from the Next.js app.

## Files Modified
- `src/app/page.tsx` - Updated to handle paginated API response
- `src/app/services/page.tsx` - Updated to handle paginated API response  
- `src/app/search/page.tsx` - Updated to handle paginated API response
- `src/types/api.ts` - Added PaginatedResponse interface
- `src/lib/api.ts` - Temporarily updated to use proxy routes
- `src/app/api/catalogue/categories/route.ts` - Created proxy route
- `src/app/api/catalogue/services/route.ts` - Created proxy route

## API Response Structure
The Django backend returns paginated responses with this structure:
```json
{
    "count": 14,
    "next": null,
    "previous": null,
    "results": [
        // ... array of items
    ]
}
```

The frontend has been updated to extract the `results` array from this paginated response structure.
