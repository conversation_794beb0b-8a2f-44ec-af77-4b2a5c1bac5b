/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/auth/login/page";
exports.ids = ["app/auth/login/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Flogin%2Fpage&page=%2Fauth%2Flogin%2Fpage&appPaths=%2Fauth%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fauth%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Flogin%2Fpage&page=%2Fauth%2Flogin%2Fpage&appPaths=%2Fauth%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fauth%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'auth',\n        {\n        children: [\n        'login',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/login/page.tsx */ \"(rsc)/./src/app/auth/login/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\layout.tsx\"],\n'error': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/error.tsx */ \"(rsc)/./src/app/error.tsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\error.tsx\"],\n'loading': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/loading.tsx */ \"(rsc)/./src/app/loading.tsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\loading.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/not-found.tsx */ \"(rsc)/./src/app/not-found.tsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\not-found.tsx\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/auth/login/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/auth/login/page\",\n        pathname: \"/auth/login\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Flogin%2Fpage&page=%2Fauth%2Flogin%2Fpage&appPaths=%2Fauth%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fauth%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Csrc%5Ccomponents%5Cui%5CToaster.tsx&modules=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Csrc%5Ccontexts%5CAuthContext.tsx&modules=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Csrc%5Ccontexts%5CCartContext.tsx&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Csrc%5Ccomponents%5Cui%5CToaster.tsx&modules=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Csrc%5Ccontexts%5CAuthContext.tsx&modules=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Csrc%5Ccontexts%5CCartContext.tsx&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/Toaster.tsx */ \"(ssr)/./src/components/ui/Toaster.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(ssr)/./src/contexts/AuthContext.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/CartContext.tsx */ \"(ssr)/./src/contexts/CartContext.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Csrc%5Ccomponents%5Cui%5CToaster.tsx&modules=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Csrc%5Ccontexts%5CAuthContext.tsx&modules=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Csrc%5Ccontexts%5CCartContext.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Csrc%5Capp%5Cauth%5Clogin%5Cpage.tsx&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Csrc%5Capp%5Cauth%5Clogin%5Cpage.tsx&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/login/page.tsx */ \"(ssr)/./src/app/auth/login/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDZG9kb2IlNUNPbmVEcml2ZSU1Q0RvY3VtZW50cyU1Q3ZpbmF5JTVDUHJvamVjdHMlNUNIb21lX3NlcnZpY2VzJTVDbmV4dF9qc19jdXN0b21lciU1Q3NyYyU1Q2FwcCU1Q2F1dGglNUNsb2dpbiU1Q3BhZ2UudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2hvbWUtc2VydmljZXMtY3VzdG9tZXIvP2JlMmQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxkb2RvYlxcXFxPbmVEcml2ZVxcXFxEb2N1bWVudHNcXFxcdmluYXlcXFxcUHJvamVjdHNcXFxcSG9tZV9zZXJ2aWNlc1xcXFxuZXh0X2pzX2N1c3RvbWVyXFxcXHNyY1xcXFxhcHBcXFxcYXV0aFxcXFxsb2dpblxcXFxwYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Csrc%5Capp%5Cauth%5Clogin%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Csrc%5Capp%5Cerror.tsx&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Csrc%5Capp%5Cerror.tsx&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/error.tsx */ \"(ssr)/./src/app/error.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDZG9kb2IlNUNPbmVEcml2ZSU1Q0RvY3VtZW50cyU1Q3ZpbmF5JTVDUHJvamVjdHMlNUNIb21lX3NlcnZpY2VzJTVDbmV4dF9qc19jdXN0b21lciU1Q3NyYyU1Q2FwcCU1Q2Vycm9yLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ob21lLXNlcnZpY2VzLWN1c3RvbWVyLz9iZDE3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcZG9kb2JcXFxcT25lRHJpdmVcXFxcRG9jdW1lbnRzXFxcXHZpbmF5XFxcXFByb2plY3RzXFxcXEhvbWVfc2VydmljZXNcXFxcbmV4dF9qc19jdXN0b21lclxcXFxzcmNcXFxcYXBwXFxcXGVycm9yLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Csrc%5Capp%5Cerror.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Csrc%5Capp%5Cloading.tsx&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Csrc%5Capp%5Cloading.tsx&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/loading.tsx */ \"(ssr)/./src/app/loading.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDZG9kb2IlNUNPbmVEcml2ZSU1Q0RvY3VtZW50cyU1Q3ZpbmF5JTVDUHJvamVjdHMlNUNIb21lX3NlcnZpY2VzJTVDbmV4dF9qc19jdXN0b21lciU1Q3NyYyU1Q2FwcCU1Q2xvYWRpbmcudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2hvbWUtc2VydmljZXMtY3VzdG9tZXIvP2RkNTUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxkb2RvYlxcXFxPbmVEcml2ZVxcXFxEb2N1bWVudHNcXFxcdmluYXlcXFxcUHJvamVjdHNcXFxcSG9tZV9zZXJ2aWNlc1xcXFxuZXh0X2pzX2N1c3RvbWVyXFxcXHNyY1xcXFxhcHBcXFxcbG9hZGluZy50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Csrc%5Capp%5Cloading.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Csrc%5Capp%5Cnot-found.tsx&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Csrc%5Capp%5Cnot-found.tsx&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/not-found.tsx */ \"(ssr)/./src/app/not-found.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDZG9kb2IlNUNPbmVEcml2ZSU1Q0RvY3VtZW50cyU1Q3ZpbmF5JTVDUHJvamVjdHMlNUNIb21lX3NlcnZpY2VzJTVDbmV4dF9qc19jdXN0b21lciU1Q3NyYyU1Q2FwcCU1Q25vdC1mb3VuZC50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaG9tZS1zZXJ2aWNlcy1jdXN0b21lci8/ZWIxMSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGRvZG9iXFxcXE9uZURyaXZlXFxcXERvY3VtZW50c1xcXFx2aW5heVxcXFxQcm9qZWN0c1xcXFxIb21lX3NlcnZpY2VzXFxcXG5leHRfanNfY3VzdG9tZXJcXFxcc3JjXFxcXGFwcFxcXFxub3QtZm91bmQudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Csrc%5Capp%5Cnot-found.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/auth/login/page.tsx":
/*!*************************************!*\
  !*** ./src/app/auth/login/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Mail,Phone!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Mail,Phone!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Mail,Phone!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_ui_Toaster__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/Toaster */ \"(ssr)/./src/components/ui/Toaster.tsx\");\n/* harmony import */ var _components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/LoadingSpinner */ \"(ssr)/./src/components/ui/LoadingSpinner.tsx\");\n/* harmony import */ var _components_auth_RedirectMessage__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/auth/RedirectMessage */ \"(ssr)/./src/components/auth/RedirectMessage.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var _lib_errorUtils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/errorUtils */ \"(ssr)/./src/lib/errorUtils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\nfunction LoginPage() {\n    const [loginMethod, setLoginMethod] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"mobile\");\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        mobile_number: \"\",\n        email: \"\",\n        password: \"\",\n        otp: \"\"\n    });\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [otpSent, setOtpSent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [otpTimer, setOtpTimer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const { login } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const { showToast } = (0,_components_ui_Toaster__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const redirectTo = searchParams.get(\"redirect\") || \"/\";\n    // Pre-fill mobile number from URL parameter if available\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const mobileFromUrl = searchParams.get(\"mobile\");\n        if (mobileFromUrl) {\n            setFormData((prev)=>({\n                    ...prev,\n                    mobile_number: mobileFromUrl\n                }));\n        }\n    }, [\n        searchParams\n    ]);\n    // Start OTP timer\n    const startOtpTimer = ()=>{\n        setOtpTimer(60);\n        const interval = setInterval(()=>{\n            setOtpTimer((prev)=>{\n                if (prev <= 1) {\n                    clearInterval(interval);\n                    return 0;\n                }\n                return prev - 1;\n            });\n        }, 1000);\n    };\n    const handleSendOTP = async ()=>{\n        if (!formData.mobile_number) {\n            showToast({\n                type: \"error\",\n                title: \"Please enter mobile number\"\n            });\n            return;\n        }\n        setIsLoading(true);\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_8__.authApi.sendOTP(formData.mobile_number);\n            setOtpSent(true);\n            startOtpTimer();\n            showToast({\n                type: \"success\",\n                title: \"OTP sent successfully\"\n            });\n        } catch (error) {\n            // Check if the error is due to unregistered mobile number\n            if ((0,_lib_errorUtils__WEBPACK_IMPORTED_MODULE_9__.isUnregisteredMobileError)(error)) {\n                showToast({\n                    type: \"error\",\n                    title: \"Mobile number not registered\",\n                    message: (0,_lib_errorUtils__WEBPACK_IMPORTED_MODULE_9__.getUserFriendlyErrorMessage)(error)\n                });\n                // Redirect to registration page after a short delay\n                setTimeout(()=>{\n                    router.push(`/auth/register?mobile=${encodeURIComponent(formData.mobile_number)}&from=login`);\n                }, 2000);\n            } else {\n                showToast({\n                    type: \"error\",\n                    title: \"Failed to send OTP\",\n                    message: (0,_lib_errorUtils__WEBPACK_IMPORTED_MODULE_9__.getUserFriendlyErrorMessage)(error)\n                });\n            }\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleResendOTP = async ()=>{\n        setIsLoading(true);\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_8__.authApi.sendOTP(formData.mobile_number);\n            startOtpTimer();\n            showToast({\n                type: \"success\",\n                title: \"OTP resent successfully\"\n            });\n        } catch (error) {\n            // Check if the error is due to unregistered mobile number\n            if ((0,_lib_errorUtils__WEBPACK_IMPORTED_MODULE_9__.isUnregisteredMobileError)(error)) {\n                showToast({\n                    type: \"error\",\n                    title: \"Mobile number not registered\",\n                    message: (0,_lib_errorUtils__WEBPACK_IMPORTED_MODULE_9__.getUserFriendlyErrorMessage)(error)\n                });\n                // Redirect to registration page after a short delay\n                setTimeout(()=>{\n                    router.push(`/auth/register?mobile=${encodeURIComponent(formData.mobile_number)}&from=login`);\n                }, 2000);\n            } else {\n                showToast({\n                    type: \"error\",\n                    title: \"Failed to resend OTP\",\n                    message: (0,_lib_errorUtils__WEBPACK_IMPORTED_MODULE_9__.getUserFriendlyErrorMessage)(error)\n                });\n            }\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleMobileLogin = async (e)=>{\n        e.preventDefault();\n        if (!formData.mobile_number || !formData.otp) {\n            showToast({\n                type: \"error\",\n                title: \"Please enter mobile number and OTP\"\n            });\n            return;\n        }\n        setIsLoading(true);\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.authApi.loginMobile(formData.mobile_number, formData.otp);\n            login(response);\n            showToast({\n                type: \"success\",\n                title: \"Login successful\"\n            });\n            router.push(redirectTo);\n        } catch (error) {\n            if ((0,_lib_errorUtils__WEBPACK_IMPORTED_MODULE_9__.isInvalidOTPError)(error)) {\n                showToast({\n                    type: \"error\",\n                    title: \"Invalid OTP\",\n                    message: (0,_lib_errorUtils__WEBPACK_IMPORTED_MODULE_9__.getUserFriendlyErrorMessage)(error)\n                });\n            } else {\n                showToast({\n                    type: \"error\",\n                    title: \"Login failed\",\n                    message: (0,_lib_errorUtils__WEBPACK_IMPORTED_MODULE_9__.getUserFriendlyErrorMessage)(error)\n                });\n            }\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleEmailLogin = async (e)=>{\n        e.preventDefault();\n        if (!formData.email || !formData.password) {\n            showToast({\n                type: \"error\",\n                title: \"Please enter email and password\"\n            });\n            return;\n        }\n        setIsLoading(true);\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.authApi.loginEmail(formData.email, formData.password);\n            login(response);\n            showToast({\n                type: \"success\",\n                title: \"Login successful\"\n            });\n            router.push(redirectTo);\n        } catch (error) {\n            if ((0,_lib_errorUtils__WEBPACK_IMPORTED_MODULE_9__.isInvalidOTPError)(error)) {\n                showToast({\n                    type: \"error\",\n                    title: \"Invalid OTP\",\n                    message: (0,_lib_errorUtils__WEBPACK_IMPORTED_MODULE_9__.getUserFriendlyErrorMessage)(error)\n                });\n            } else {\n                showToast({\n                    type: \"error\",\n                    title: \"Login failed\",\n                    message: (0,_lib_errorUtils__WEBPACK_IMPORTED_MODULE_9__.getUserFriendlyErrorMessage)(error)\n                });\n            }\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sm:mx-auto sm:w-full sm:max-w-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                        href: \"/\",\n                        className: \"flex items-center justify-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-5 w-5 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 11\n                            }, this),\n                            \"Back to Home\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-12 h-12 bg-primary-600 rounded-lg flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-white font-bold text-lg\",\n                                children: \"HS\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-center text-3xl font-bold text-gray-900\",\n                        children: \"Sign in to your account\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-2 text-center text-sm text-gray-600\",\n                        children: [\n                            \"Or\",\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: \"/auth/register\",\n                                className: \"font-medium text-primary-600 hover:text-primary-500\",\n                                children: \"create a new account\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                lineNumber: 163,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 sm:mx-auto sm:w-full sm:max-w-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_RedirectMessage__WEBPACK_IMPORTED_MODULE_7__.RedirectMessage, {\n                        type: \"login\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex rounded-lg bg-gray-100 p-1 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setLoginMethod(\"mobile\"),\n                                        className: `flex-1 flex items-center justify-center py-2 px-3 rounded-md text-sm font-medium transition-colors ${loginMethod === \"mobile\" ? \"bg-white text-primary-600 shadow-sm\" : \"text-gray-500 hover:text-gray-700\"}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Mobile\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setLoginMethod(\"email\"),\n                                        className: `flex-1 flex items-center justify-center py-2 px-3 rounded-md text-sm font-medium transition-colors ${loginMethod === \"email\" ? \"bg-white text-primary-600 shadow-sm\" : \"text-gray-500 hover:text-gray-700\"}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Email\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 11\n                            }, this),\n                            loginMethod === \"mobile\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleMobileLogin,\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"mobile\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Mobile Number\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"mobile\",\n                                                    name: \"mobile\",\n                                                    type: \"tel\",\n                                                    required: true,\n                                                    value: formData.mobile_number,\n                                                    onChange: (e)=>setFormData({\n                                                            ...formData,\n                                                            mobile_number: e.target.value\n                                                        }),\n                                                    placeholder: \"+91 98765 43210\",\n                                                    className: \"input\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 15\n                                    }, this),\n                                    !otpSent ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_6__.LoadingButton, {\n                                        type: \"button\",\n                                        onClick: handleSendOTP,\n                                        isLoading: isLoading,\n                                        className: \"w-full btn-primary\",\n                                        children: \"Send OTP\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"otp\",\n                                                        className: \"block text-sm font-medium text-gray-700\",\n                                                        children: \"Enter OTP\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            id: \"otp\",\n                                                            name: \"otp\",\n                                                            type: \"text\",\n                                                            required: true,\n                                                            value: formData.otp,\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    otp: e.target.value\n                                                                }),\n                                                            placeholder: \"123456\",\n                                                            className: \"input\",\n                                                            maxLength: 6\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 252,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_6__.LoadingButton, {\n                                                        type: \"submit\",\n                                                        isLoading: isLoading,\n                                                        className: \"flex-1 btn-primary\",\n                                                        children: \"Verify & Login\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    otpTimer === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_6__.LoadingButton, {\n                                                        type: \"button\",\n                                                        onClick: handleResendOTP,\n                                                        isLoading: isLoading,\n                                                        className: \"btn-outline\",\n                                                        children: \"Resend\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        disabled: true,\n                                                        className: \"btn-outline opacity-50 cursor-not-allowed\",\n                                                        children: [\n                                                            otpTimer,\n                                                            \"s\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 13\n                            }, this),\n                            loginMethod === \"email\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleEmailLogin,\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"email\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Email address\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"email\",\n                                                    name: \"email\",\n                                                    type: \"email\",\n                                                    autoComplete: \"email\",\n                                                    required: true,\n                                                    value: formData.email,\n                                                    onChange: (e)=>setFormData({\n                                                            ...formData,\n                                                            email: e.target.value\n                                                        }),\n                                                    className: \"input\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"password\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Password\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"password\",\n                                                    name: \"password\",\n                                                    type: \"password\",\n                                                    autoComplete: \"current-password\",\n                                                    required: true,\n                                                    value: formData.password,\n                                                    onChange: (e)=>setFormData({\n                                                            ...formData,\n                                                            password: e.target.value\n                                                        }),\n                                                    className: \"input\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_6__.LoadingButton, {\n                                        type: \"submit\",\n                                        isLoading: isLoading,\n                                        className: \"w-full btn-primary\",\n                                        children: \"Sign in\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                lineNumber: 186,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n        lineNumber: 162,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/auth/login/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/error.tsx":
/*!***************************!*\
  !*** ./src/app/error.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Error)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Error({ error, reset }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"sm:mx-auto sm:w-full sm:max-w-md\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"h-8 w-8 text-red-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\error.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\error.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-center text-3xl font-bold text-gray-900 mb-4\",\n                    children: \"Something went wrong!\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\error.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-center text-gray-600 mb-8\",\n                    children: \"We encountered an unexpected error. Please try again or contact support if the problem persists.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\error.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: reset,\n                            className: \"w-full btn-primary flex items-center justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\error.tsx\",\n                                    lineNumber: 36,\n                                    columnNumber: 13\n                                }, this),\n                                \"Try Again\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"w-full btn-secondary flex items-center justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\error.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 13\n                                }, this),\n                                \"Go Home\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\error.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 9\n                }, this),\n                 true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 p-4 bg-red-50 border border-red-200 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-sm font-medium text-red-800 mb-2\",\n                            children: \"Error Details (Development)\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-red-700 font-mono\",\n                            children: error.message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\error.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\error.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\error.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/error.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/loading.tsx":
/*!*****************************!*\
  !*** ./src/app/loading.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Loading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/LoadingSpinner */ \"(ssr)/./src/components/ui/LoadingSpinner.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Loading() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex flex-col justify-center items-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-12 h-12 bg-primary-600 rounded-lg flex items-center justify-center mb-6 mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-white font-bold text-lg\",\n                        children: \"HS\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\loading.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\loading.tsx\",\n                    lineNumber: 10,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_2__.LoadingSpinner, {\n                    size: \"lg\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\loading.tsx\",\n                    lineNumber: 13,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 mt-4\",\n                    children: \"Loading...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\loading.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\loading.tsx\",\n            lineNumber: 9,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\loading.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2xvYWRpbmcudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFFMEI7QUFDc0M7QUFFakQsU0FBU0U7SUFDdEIscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7a0JBQ2IsNEVBQUNEO1lBQUlDLFdBQVU7OzhCQUNiLDhEQUFDRDtvQkFBSUMsV0FBVTs4QkFDYiw0RUFBQ0M7d0JBQUtELFdBQVU7a0NBQStCOzs7Ozs7Ozs7Ozs4QkFFakQsOERBQUNILHlFQUFjQTtvQkFBQ0ssTUFBSzs7Ozs7OzhCQUNyQiw4REFBQ0M7b0JBQUVILFdBQVU7OEJBQXFCOzs7Ozs7Ozs7Ozs7Ozs7OztBQUkxQyIsInNvdXJjZXMiOlsid2VicGFjazovL2hvbWUtc2VydmljZXMtY3VzdG9tZXIvLi9zcmMvYXBwL2xvYWRpbmcudHN4PzljZDkiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgTG9hZGluZ1NwaW5uZXIgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvTG9hZGluZ1NwaW5uZXInO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBMb2FkaW5nKCkge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYXktNTAgZmxleCBmbGV4LWNvbCBqdXN0aWZ5LWNlbnRlciBpdGVtcy1jZW50ZXJcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTEyIGgtMTIgYmctcHJpbWFyeS02MDAgcm91bmRlZC1sZyBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBtYi02IG14LWF1dG9cIj5cbiAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtYm9sZCB0ZXh0LWxnXCI+SFM8L3NwYW4+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICA8TG9hZGluZ1NwaW5uZXIgc2l6ZT1cImxnXCIgLz5cbiAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBtdC00XCI+TG9hZGluZy4uLjwvcD5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiTG9hZGluZ1NwaW5uZXIiLCJMb2FkaW5nIiwiZGl2IiwiY2xhc3NOYW1lIiwic3BhbiIsInNpemUiLCJwIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/loading.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/not-found.tsx":
/*!*******************************!*\
  !*** ./src/app/not-found.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Home_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Home,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Home_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Home,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Home_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Home,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"sm:mx-auto sm:w-full sm:max-w-md text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Home_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"h-12 w-12 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 13,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\not-found.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\not-found.tsx\",\n                    lineNumber: 11,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-6xl font-bold text-gray-900 mb-4\",\n                    children: \"404\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\not-found.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold text-gray-900 mb-4\",\n                    children: \"Page Not Found\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\not-found.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 mb-8\",\n                    children: \"The page you're looking for doesn't exist or has been moved.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\not-found.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"w-full btn-primary flex items-center justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Home_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 29,\n                                    columnNumber: 13\n                                }, this),\n                                \"Go Home\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>window.history.back(),\n                            className: \"w-full btn-secondary flex items-center justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Home_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 13\n                                }, this),\n                                \"Go Back\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\not-found.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-500\",\n                        children: [\n                            \"Need help? \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/contact\",\n                                className: \"text-primary-600 hover:text-primary-700\",\n                                children: \"Contact Support\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\not-found.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 24\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\not-found.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\not-found.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\not-found.tsx\",\n            lineNumber: 10,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\not-found.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/not-found.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/RedirectMessage.tsx":
/*!*************************************************!*\
  !*** ./src/components/auth/RedirectMessage.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RedirectMessage: () => (/* binding */ RedirectMessage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_LogIn_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=LogIn,UserPlus!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_LogIn_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=LogIn,UserPlus!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-in.js\");\n/* __next_internal_client_entry_do_not_use__ RedirectMessage auto */ \n\n\n\nconst RedirectMessage = ({ type })=>{\n    const [showMessage, setShowMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const mobile = searchParams.get(\"mobile\");\n        const fromRedirect = searchParams.get(\"from\");\n        if (mobile && fromRedirect) {\n            setShowMessage(true);\n            if (type === \"register\" && fromRedirect === \"login\") {\n                setMessage(`Mobile number ${mobile} is not registered. Please create an account to continue.`);\n            } else if (type === \"login\" && fromRedirect === \"register\") {\n                setMessage(`Mobile number ${mobile} is already registered. Please login to continue.`);\n            }\n            // Auto-hide message after 10 seconds\n            const timer = setTimeout(()=>{\n                setShowMessage(false);\n            }, 10000);\n            return ()=>clearTimeout(timer);\n        }\n    }, [\n        searchParams,\n        type\n    ]);\n    if (!showMessage) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start space-x-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0\",\n                    children: type === \"register\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogIn_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"h-5 w-5 text-blue-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\components\\\\auth\\\\RedirectMessage.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 13\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogIn_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"h-5 w-5 text-blue-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\components\\\\auth\\\\RedirectMessage.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\components\\\\auth\\\\RedirectMessage.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-sm font-medium text-blue-900 mb-1\",\n                            children: type === \"register\" ? \"Registration Required\" : \"Login Required\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\components\\\\auth\\\\RedirectMessage.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-blue-700\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\components\\\\auth\\\\RedirectMessage.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\components\\\\auth\\\\RedirectMessage.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>setShowMessage(false),\n                    className: \"flex-shrink-0 text-blue-400 hover:text-blue-600\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"sr-only\",\n                            children: \"Close\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\components\\\\auth\\\\RedirectMessage.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"h-4 w-4\",\n                            fill: \"none\",\n                            viewBox: \"0 0 24 24\",\n                            stroke: \"currentColor\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M6 18L18 6M6 6l12 12\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\components\\\\auth\\\\RedirectMessage.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\components\\\\auth\\\\RedirectMessage.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\components\\\\auth\\\\RedirectMessage.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\components\\\\auth\\\\RedirectMessage.tsx\",\n            lineNumber: 42,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\components\\\\auth\\\\RedirectMessage.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9hdXRoL1JlZGlyZWN0TWVzc2FnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUVtRDtBQUNEO0FBQ1U7QUFNckQsTUFBTU0sa0JBQWtELENBQUMsRUFBRUMsSUFBSSxFQUFFO0lBQ3RFLE1BQU0sQ0FBQ0MsYUFBYUMsZUFBZSxHQUFHUCwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUNRLFNBQVNDLFdBQVcsR0FBR1QsK0NBQVFBLENBQUM7SUFDdkMsTUFBTVUsZUFBZVQsZ0VBQWVBO0lBRXBDRixnREFBU0EsQ0FBQztRQUNSLE1BQU1ZLFNBQVNELGFBQWFFLEdBQUcsQ0FBQztRQUNoQyxNQUFNQyxlQUFlSCxhQUFhRSxHQUFHLENBQUM7UUFFdEMsSUFBSUQsVUFBVUUsY0FBYztZQUMxQk4sZUFBZTtZQUVmLElBQUlGLFNBQVMsY0FBY1EsaUJBQWlCLFNBQVM7Z0JBQ25ESixXQUFXLENBQUMsY0FBYyxFQUFFRSxPQUFPLHlEQUF5RCxDQUFDO1lBQy9GLE9BQU8sSUFBSU4sU0FBUyxXQUFXUSxpQkFBaUIsWUFBWTtnQkFDMURKLFdBQVcsQ0FBQyxjQUFjLEVBQUVFLE9BQU8saURBQWlELENBQUM7WUFDdkY7WUFFQSxxQ0FBcUM7WUFDckMsTUFBTUcsUUFBUUMsV0FBVztnQkFDdkJSLGVBQWU7WUFDakIsR0FBRztZQUVILE9BQU8sSUFBTVMsYUFBYUY7UUFDNUI7SUFDRixHQUFHO1FBQUNKO1FBQWNMO0tBQUs7SUFFdkIsSUFBSSxDQUFDQyxhQUFhLE9BQU87SUFFekIscUJBQ0UsOERBQUNXO1FBQUlDLFdBQVU7a0JBQ2IsNEVBQUNEO1lBQUlDLFdBQVU7OzhCQUNiLDhEQUFDRDtvQkFBSUMsV0FBVTs4QkFDWmIsU0FBUywyQkFDUiw4REFBQ0gsMEZBQVFBO3dCQUFDZ0IsV0FBVTs7Ozs7a0RBRXBCLDhEQUFDZiwwRkFBS0E7d0JBQUNlLFdBQVU7Ozs7Ozs7Ozs7OzhCQUdyQiw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDQzs0QkFBR0QsV0FBVTtzQ0FDWGIsU0FBUyxhQUFhLDBCQUEwQjs7Ozs7O3NDQUVuRCw4REFBQ2U7NEJBQUVGLFdBQVU7c0NBQXlCVjs7Ozs7Ozs7Ozs7OzhCQUV4Qyw4REFBQ2E7b0JBQ0NDLFNBQVMsSUFBTWYsZUFBZTtvQkFDOUJXLFdBQVU7O3NDQUVWLDhEQUFDSzs0QkFBS0wsV0FBVTtzQ0FBVTs7Ozs7O3NDQUMxQiw4REFBQ007NEJBQUlOLFdBQVU7NEJBQVVPLE1BQUs7NEJBQU9DLFNBQVE7NEJBQVlDLFFBQU87c0NBQzlELDRFQUFDQztnQ0FBS0MsZUFBYztnQ0FBUUMsZ0JBQWU7Z0NBQVFDLGFBQWE7Z0NBQUdDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFNakYsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL2hvbWUtc2VydmljZXMtY3VzdG9tZXIvLi9zcmMvY29tcG9uZW50cy9hdXRoL1JlZGlyZWN0TWVzc2FnZS50c3g/YzU5MyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlU2VhcmNoUGFyYW1zIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJztcbmltcG9ydCB7IEFsZXJ0Q2lyY2xlLCBVc2VyUGx1cywgTG9nSW4gfSBmcm9tICdsdWNpZGUtcmVhY3QnO1xuXG5pbnRlcmZhY2UgUmVkaXJlY3RNZXNzYWdlUHJvcHMge1xuICB0eXBlOiAnbG9naW4nIHwgJ3JlZ2lzdGVyJztcbn1cblxuZXhwb3J0IGNvbnN0IFJlZGlyZWN0TWVzc2FnZTogUmVhY3QuRkM8UmVkaXJlY3RNZXNzYWdlUHJvcHM+ID0gKHsgdHlwZSB9KSA9PiB7XG4gIGNvbnN0IFtzaG93TWVzc2FnZSwgc2V0U2hvd01lc3NhZ2VdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbbWVzc2FnZSwgc2V0TWVzc2FnZV0gPSB1c2VTdGF0ZSgnJyk7XG4gIGNvbnN0IHNlYXJjaFBhcmFtcyA9IHVzZVNlYXJjaFBhcmFtcygpO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgbW9iaWxlID0gc2VhcmNoUGFyYW1zLmdldCgnbW9iaWxlJyk7XG4gICAgY29uc3QgZnJvbVJlZGlyZWN0ID0gc2VhcmNoUGFyYW1zLmdldCgnZnJvbScpO1xuXG4gICAgaWYgKG1vYmlsZSAmJiBmcm9tUmVkaXJlY3QpIHtcbiAgICAgIHNldFNob3dNZXNzYWdlKHRydWUpO1xuICAgICAgXG4gICAgICBpZiAodHlwZSA9PT0gJ3JlZ2lzdGVyJyAmJiBmcm9tUmVkaXJlY3QgPT09ICdsb2dpbicpIHtcbiAgICAgICAgc2V0TWVzc2FnZShgTW9iaWxlIG51bWJlciAke21vYmlsZX0gaXMgbm90IHJlZ2lzdGVyZWQuIFBsZWFzZSBjcmVhdGUgYW4gYWNjb3VudCB0byBjb250aW51ZS5gKTtcbiAgICAgIH0gZWxzZSBpZiAodHlwZSA9PT0gJ2xvZ2luJyAmJiBmcm9tUmVkaXJlY3QgPT09ICdyZWdpc3RlcicpIHtcbiAgICAgICAgc2V0TWVzc2FnZShgTW9iaWxlIG51bWJlciAke21vYmlsZX0gaXMgYWxyZWFkeSByZWdpc3RlcmVkLiBQbGVhc2UgbG9naW4gdG8gY29udGludWUuYCk7XG4gICAgICB9XG5cbiAgICAgIC8vIEF1dG8taGlkZSBtZXNzYWdlIGFmdGVyIDEwIHNlY29uZHNcbiAgICAgIGNvbnN0IHRpbWVyID0gc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgIHNldFNob3dNZXNzYWdlKGZhbHNlKTtcbiAgICAgIH0sIDEwMDAwKTtcblxuICAgICAgcmV0dXJuICgpID0+IGNsZWFyVGltZW91dCh0aW1lcik7XG4gICAgfVxuICB9LCBbc2VhcmNoUGFyYW1zLCB0eXBlXSk7XG5cbiAgaWYgKCFzaG93TWVzc2FnZSkgcmV0dXJuIG51bGw7XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTYgcC00IGJnLWJsdWUtNTAgYm9yZGVyIGJvcmRlci1ibHVlLTIwMCByb3VuZGVkLWxnXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQgc3BhY2UteC0zXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC1zaHJpbmstMFwiPlxuICAgICAgICAgIHt0eXBlID09PSAncmVnaXN0ZXInID8gKFxuICAgICAgICAgICAgPFVzZXJQbHVzIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1ibHVlLTYwMFwiIC8+XG4gICAgICAgICAgKSA6IChcbiAgICAgICAgICAgIDxMb2dJbiBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtYmx1ZS02MDBcIiAvPlxuICAgICAgICAgICl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtYmx1ZS05MDAgbWItMVwiPlxuICAgICAgICAgICAge3R5cGUgPT09ICdyZWdpc3RlcicgPyAnUmVnaXN0cmF0aW9uIFJlcXVpcmVkJyA6ICdMb2dpbiBSZXF1aXJlZCd9XG4gICAgICAgICAgPC9oMz5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtYmx1ZS03MDBcIj57bWVzc2FnZX08L3A+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd01lc3NhZ2UoZmFsc2UpfVxuICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXgtc2hyaW5rLTAgdGV4dC1ibHVlLTQwMCBob3Zlcjp0ZXh0LWJsdWUtNjAwXCJcbiAgICAgICAgPlxuICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInNyLW9ubHlcIj5DbG9zZTwvc3Bhbj5cbiAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cImgtNCB3LTRcIiBmaWxsPVwibm9uZVwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIj5cbiAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk02IDE4TDE4IDZNNiA2bDEyIDEyXCIgLz5cbiAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgPC9idXR0b24+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn07XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsInVzZVNlYXJjaFBhcmFtcyIsIlVzZXJQbHVzIiwiTG9nSW4iLCJSZWRpcmVjdE1lc3NhZ2UiLCJ0eXBlIiwic2hvd01lc3NhZ2UiLCJzZXRTaG93TWVzc2FnZSIsIm1lc3NhZ2UiLCJzZXRNZXNzYWdlIiwic2VhcmNoUGFyYW1zIiwibW9iaWxlIiwiZ2V0IiwiZnJvbVJlZGlyZWN0IiwidGltZXIiLCJzZXRUaW1lb3V0IiwiY2xlYXJUaW1lb3V0IiwiZGl2IiwiY2xhc3NOYW1lIiwiaDMiLCJwIiwiYnV0dG9uIiwib25DbGljayIsInNwYW4iLCJzdmciLCJmaWxsIiwidmlld0JveCIsInN0cm9rZSIsInBhdGgiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iLCJzdHJva2VXaWR0aCIsImQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/RedirectMessage.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/LoadingSpinner.tsx":
/*!**********************************************!*\
  !*** ./src/components/ui/LoadingSpinner.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LoadingButton: () => (/* binding */ LoadingButton),\n/* harmony export */   LoadingPage: () => (/* binding */ LoadingPage),\n/* harmony export */   LoadingSpinner: () => (/* binding */ LoadingSpinner)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ LoadingSpinner,LoadingPage,LoadingButton auto */ \n\nconst LoadingSpinner = ({ size = \"md\", className = \"\" })=>{\n    const sizeClasses = {\n        sm: \"w-4 h-4\",\n        md: \"w-6 h-6\",\n        lg: \"w-8 h-8\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${sizeClasses[size]} ${className}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"spinner\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\components\\\\ui\\\\LoadingSpinner.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\components\\\\ui\\\\LoadingSpinner.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, undefined);\n};\nconst LoadingPage = ({ message = \"Loading...\" })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center justify-center min-h-[400px] space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingSpinner, {\n                size: \"lg\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\components\\\\ui\\\\LoadingSpinner.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-gray-600\",\n                children: message\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\components\\\\ui\\\\LoadingSpinner.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\components\\\\ui\\\\LoadingSpinner.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, undefined);\n};\nconst LoadingButton = ({ isLoading, children, className = \"\", disabled = false, onClick, type = \"button\" })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        type: type,\n        onClick: onClick,\n        disabled: disabled || isLoading,\n        className: `${className} ${isLoading || disabled ? \"opacity-50 cursor-not-allowed\" : \"\"} flex items-center justify-center space-x-2`,\n        children: [\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingSpinner, {\n                size: \"sm\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\components\\\\ui\\\\LoadingSpinner.tsx\",\n                lineNumber: 62,\n                columnNumber: 21\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\components\\\\ui\\\\LoadingSpinner.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\components\\\\ui\\\\LoadingSpinner.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/LoadingSpinner.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Toaster.tsx":
/*!***************************************!*\
  !*** ./src/components/ui/Toaster.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   Toaster: () => (/* binding */ Toaster),\n/* harmony export */   useToast: () => (/* binding */ useToast),\n/* harmony export */   useToastNotification: () => (/* binding */ useToastNotification)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ useToast,ToastProvider,Toaster,useToastNotification auto */ \n\n\nconst ToastContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useToast = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ToastContext);\n    if (!context) {\n        throw new Error(\"useToast must be used within a ToastProvider\");\n    }\n    return context;\n};\nconst ToastIcon = ({ type })=>{\n    const iconProps = {\n        className: \"w-5 h-5\"\n    };\n    switch(type){\n        case \"success\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                ...iconProps,\n                className: \"w-5 h-5 text-green-500\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\components\\\\ui\\\\Toaster.tsx\",\n                lineNumber: 36,\n                columnNumber: 14\n            }, undefined);\n        case \"error\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                ...iconProps,\n                className: \"w-5 h-5 text-red-500\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\components\\\\ui\\\\Toaster.tsx\",\n                lineNumber: 38,\n                columnNumber: 14\n            }, undefined);\n        case \"warning\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                ...iconProps,\n                className: \"w-5 h-5 text-yellow-500\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\components\\\\ui\\\\Toaster.tsx\",\n                lineNumber: 40,\n                columnNumber: 14\n            }, undefined);\n        case \"info\":\n        default:\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                ...iconProps,\n                className: \"w-5 h-5 text-blue-500\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\components\\\\ui\\\\Toaster.tsx\",\n                lineNumber: 43,\n                columnNumber: 14\n            }, undefined);\n    }\n};\nconst ToastItem = ({ toast, onClose })=>{\n    const bgColor = {\n        success: \"bg-green-50 border-green-200\",\n        error: \"bg-red-50 border-red-200\",\n        warning: \"bg-yellow-50 border-yellow-200\",\n        info: \"bg-blue-50 border-blue-200\"\n    }[toast.type];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${bgColor} border rounded-lg p-4 shadow-lg toast-enter max-w-sm w-full`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastIcon, {\n                        type: toast.type\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\components\\\\ui\\\\Toaster.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\components\\\\ui\\\\Toaster.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"ml-3 flex-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm font-medium text-gray-900\",\n                            children: toast.title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\components\\\\ui\\\\Toaster.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, undefined),\n                        toast.message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-1 text-sm text-gray-600\",\n                            children: toast.message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\components\\\\ui\\\\Toaster.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\components\\\\ui\\\\Toaster.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"ml-4 flex-shrink-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>onClose(toast.id),\n                        className: \"inline-flex text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\components\\\\ui\\\\Toaster.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\components\\\\ui\\\\Toaster.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\components\\\\ui\\\\Toaster.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\components\\\\ui\\\\Toaster.tsx\",\n            lineNumber: 57,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\components\\\\ui\\\\Toaster.tsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, undefined);\n};\nconst ToastProvider = ({ children })=>{\n    const [toasts, setToasts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const showToast = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((toast)=>{\n        const id = Math.random().toString(36).substr(2, 9);\n        const newToast = {\n            ...toast,\n            id\n        };\n        setToasts((prev)=>[\n                ...prev,\n                newToast\n            ]);\n        // Auto-remove toast after duration\n        const duration = toast.duration || 5000;\n        setTimeout(()=>{\n            setToasts((prev)=>prev.filter((t)=>t.id !== id));\n        }, duration);\n    }, []);\n    const hideToast = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((id)=>{\n        setToasts((prev)=>prev.filter((t)=>t.id !== id));\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContext.Provider, {\n        value: {\n            showToast,\n            hideToast\n        },\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-4 right-4 z-50 space-y-2\",\n                children: toasts.map((toast)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastItem, {\n                        toast: toast,\n                        onClose: hideToast\n                    }, toast.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\components\\\\ui\\\\Toaster.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\components\\\\ui\\\\Toaster.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\components\\\\ui\\\\Toaster.tsx\",\n        lineNumber: 101,\n        columnNumber: 5\n    }, undefined);\n};\nconst Toaster = ToastProvider;\n// Hook for easy toast usage\nconst useToastNotification = ()=>{\n    const { showToast } = useToast();\n    return {\n        success: (title, message)=>showToast({\n                type: \"success\",\n                title,\n                message\n            }),\n        error: (title, message)=>showToast({\n                type: \"error\",\n                title,\n                message\n            }),\n        info: (title, message)=>showToast({\n                type: \"info\",\n                title,\n                message\n            }),\n        warning: (title, message)=>showToast({\n                type: \"warning\",\n                title,\n                message\n            })\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Toaster.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const isAuthenticated = !!user;\n    // Initialize auth state on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeAuth = async ()=>{\n            const { refresh } = (0,_lib_api__WEBPACK_IMPORTED_MODULE_2__.getTokens)();\n            if (refresh) {\n                try {\n                    // Try to get user profile to verify token validity\n                    const userProfile = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.getProfile();\n                    setUser(userProfile);\n                } catch (error) {\n                    // Token is invalid, clear it\n                    (0,_lib_api__WEBPACK_IMPORTED_MODULE_2__.clearTokens)();\n                }\n            }\n            setIsLoading(false);\n        };\n        initializeAuth();\n    }, []);\n    const login = (response)=>{\n        (0,_lib_api__WEBPACK_IMPORTED_MODULE_2__.setTokens)(response.tokens);\n        setUser(response.user);\n    };\n    const logout = async ()=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.logout();\n        } catch (error) {\n            // Even if logout fails on server, clear local tokens\n            console.error(\"Logout error:\", error);\n        } finally{\n            (0,_lib_api__WEBPACK_IMPORTED_MODULE_2__.clearTokens)();\n            setUser(null);\n        }\n    };\n    const updateUser = (userData)=>{\n        if (user) {\n            setUser({\n                ...user,\n                ...userData\n            });\n        }\n    };\n    const refreshUserProfile = async ()=>{\n        if (isAuthenticated) {\n            try {\n                const userProfile = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.getProfile();\n                setUser(userProfile);\n            } catch (error) {\n                console.error(\"Failed to refresh user profile:\", error);\n            }\n        }\n    };\n    const value = {\n        user,\n        isAuthenticated,\n        isLoading,\n        login,\n        logout,\n        updateUser,\n        refreshUserProfile\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 104,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/CartContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/CartContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CartProvider: () => (/* binding */ CartProvider),\n/* harmony export */   useCart: () => (/* binding */ useCart)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ useCart,CartProvider auto */ \n\n\n\nconst CartContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useCart = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(CartContext);\n    if (context === undefined) {\n        throw new Error(\"useCart must be used within a CartProvider\");\n    }\n    return context;\n};\nconst CartProvider = ({ children })=>{\n    const [cart, setCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [cartSummary, setCartSummary] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { isAuthenticated } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    // Fetch cart data\n    const refreshCart = async ()=>{\n        setIsLoading(true);\n        try {\n            const [cartData, summaryData] = await Promise.all([\n                _lib_api__WEBPACK_IMPORTED_MODULE_2__.cartApi.getCart(),\n                _lib_api__WEBPACK_IMPORTED_MODULE_2__.cartApi.getCartSummary()\n            ]);\n            setCart(cartData);\n            setCartSummary(summaryData);\n        } catch (error) {\n            console.error(\"Failed to fetch cart:\", error);\n            // Initialize empty cart state\n            setCart(null);\n            setCartSummary(null);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Initialize cart on mount and when auth state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        refreshCart();\n    }, [\n        isAuthenticated\n    ]);\n    const addToCart = async (service, quantity = 1)=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.cartApi.addToCart(service.id, quantity);\n            await refreshCart();\n        } catch (error) {\n            console.error(\"Failed to add to cart:\", error);\n            throw error;\n        }\n    };\n    const updateCartItem = async (itemId, quantity)=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.cartApi.updateCartItem(itemId, quantity);\n            await refreshCart();\n        } catch (error) {\n            console.error(\"Failed to update cart item:\", error);\n            throw error;\n        }\n    };\n    const removeCartItem = async (itemId)=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.cartApi.removeCartItem(itemId);\n            await refreshCart();\n        } catch (error) {\n            console.error(\"Failed to remove cart item:\", error);\n            throw error;\n        }\n    };\n    const clearCart = async ()=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.cartApi.clearCart();\n            await refreshCart();\n        } catch (error) {\n            console.error(\"Failed to clear cart:\", error);\n            throw error;\n        }\n    };\n    const applyCoupon = async (couponCode)=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.cartApi.applyCoupon(couponCode);\n            await refreshCart();\n        } catch (error) {\n            console.error(\"Failed to apply coupon:\", error);\n            throw error;\n        }\n    };\n    const removeCoupon = async ()=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.cartApi.removeCoupon();\n            await refreshCart();\n        } catch (error) {\n            console.error(\"Failed to remove coupon:\", error);\n            throw error;\n        }\n    };\n    const getTotalItems = ()=>{\n        return cartSummary?.items_count || 0;\n    };\n    const value = {\n        cart,\n        cartSummary,\n        isLoading,\n        addToCart,\n        updateCartItem,\n        removeCartItem,\n        clearCart,\n        applyCoupon,\n        removeCoupon,\n        refreshCart,\n        getTotalItems\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CartContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\contexts\\\\CartContext.tsx\",\n        lineNumber: 146,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/CartContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiRequest: () => (/* binding */ apiRequest),\n/* harmony export */   authApi: () => (/* binding */ authApi),\n/* harmony export */   cartApi: () => (/* binding */ cartApi),\n/* harmony export */   catalogueApi: () => (/* binding */ catalogueApi),\n/* harmony export */   clearTokens: () => (/* binding */ clearTokens),\n/* harmony export */   couponApi: () => (/* binding */ couponApi),\n/* harmony export */   getTokens: () => (/* binding */ getTokens),\n/* harmony export */   orderApi: () => (/* binding */ orderApi),\n/* harmony export */   paymentApi: () => (/* binding */ paymentApi),\n/* harmony export */   setTokens: () => (/* binding */ setTokens)\n/* harmony export */ });\n// Use proxy route to avoid CORS issues in development\nconst API_BASE_URL =  true ? \"/api/proxy\" : 0;\n// Token management\nlet accessToken = null;\nlet refreshToken = null;\nconst setTokens = (tokens)=>{\n    accessToken = tokens.access;\n    refreshToken = tokens.refresh;\n    if (false) {}\n};\nconst getTokens = ()=>{\n    if (false) {}\n    return {\n        access: accessToken,\n        refresh: refreshToken\n    };\n};\nconst clearTokens = ()=>{\n    accessToken = null;\n    refreshToken = null;\n    if (false) {}\n};\n// Token refresh function\nconst refreshAccessToken = async ()=>{\n    const { refresh } = getTokens();\n    if (!refresh) return false;\n    try {\n        const response = await fetch(`${API_BASE_URL}/auth/token/refresh/`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                refresh\n            })\n        });\n        if (response.ok) {\n            const data = await response.json();\n            setTokens(data);\n            return true;\n        } else {\n            clearTokens();\n            return false;\n        }\n    } catch (error) {\n        clearTokens();\n        return false;\n    }\n};\n// API request wrapper with automatic token refresh\nconst apiRequest = async (endpoint, options = {})=>{\n    const url = endpoint.startsWith(\"http\") ? endpoint : `${API_BASE_URL}${endpoint}`;\n    const { access } = getTokens();\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        ...options.headers\n    };\n    if (access) {\n        headers.Authorization = `Bearer ${access}`;\n    }\n    let response = await fetch(url, {\n        ...options,\n        headers\n    });\n    // If unauthorized and we have a refresh token, try to refresh\n    if (response.status === 401 && refreshToken) {\n        const refreshed = await refreshAccessToken();\n        if (refreshed) {\n            const { access: newAccess } = getTokens();\n            headers.Authorization = `Bearer ${newAccess}`;\n            response = await fetch(url, {\n                ...options,\n                headers\n            });\n        }\n    }\n    if (!response.ok) {\n        const errorData = await response.json().catch(()=>({}));\n        const error = {\n            message: errorData.message || errorData.detail || errorData.error || `HTTP ${response.status}`,\n            details: errorData,\n            status: response.status\n        };\n        throw error;\n    }\n    // Handle empty responses (like DELETE operations)\n    if (response.status === 204) {\n        return {};\n    }\n    return response.json();\n};\n// Specific API functions\nconst authApi = {\n    // Register with mobile\n    registerMobile: (data)=>apiRequest(\"/auth/register/mobile/\", {\n            method: \"POST\",\n            body: JSON.stringify(data)\n        }),\n    // Send OTP\n    sendOTP: (mobile_number)=>apiRequest(\"/auth/otp/send/\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                mobile_number\n            })\n        }),\n    // Verify OTP\n    verifyOTP: (mobile_number, otp)=>apiRequest(\"/auth/otp/verify/\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                mobile_number,\n                otp\n            })\n        }),\n    // Login with mobile/OTP\n    loginMobile: (mobile_number, otp)=>apiRequest(\"/auth/login/mobile/\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                mobile_number,\n                otp\n            })\n        }),\n    // Login with email/password\n    loginEmail: (email, password)=>apiRequest(\"/auth/login/email/\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                email,\n                password\n            })\n        }),\n    // Get user profile\n    getProfile: ()=>apiRequest(\"/auth/profile/\"),\n    // Update user profile\n    updateProfile: (data)=>apiRequest(\"/auth/profile/\", {\n            method: \"PUT\",\n            body: JSON.stringify(data)\n        }),\n    // Logout\n    logout: ()=>{\n        const { refresh } = getTokens();\n        return apiRequest(\"/auth/logout/\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                refresh\n            })\n        });\n    },\n    // Address management\n    getAddresses: ()=>apiRequest(\"/auth/addresses/\"),\n    createAddress: (data)=>apiRequest(\"/auth/addresses/\", {\n            method: \"POST\",\n            body: JSON.stringify(data)\n        }),\n    updateAddress: (id, data)=>apiRequest(`/auth/addresses/${id}/`, {\n            method: \"PUT\",\n            body: JSON.stringify(data)\n        }),\n    deleteAddress: (id)=>apiRequest(`/auth/addresses/${id}/`, {\n            method: \"DELETE\"\n        })\n};\nconst catalogueApi = {\n    // Categories\n    getCategories: (params)=>{\n        const query = params ? `?${new URLSearchParams(params).toString()}` : \"\";\n        return apiRequest(`/catalogue/categories/${query}`);\n    },\n    getCategoryDetail: (slug)=>apiRequest(`/catalogue/categories/${slug}/`),\n    getCategoryTree: ()=>apiRequest(\"/catalogue/categories/tree/\"),\n    getCategoryServices: (slug)=>apiRequest(`/catalogue/categories/${slug}/services/`),\n    // Services\n    getServices: (params)=>{\n        const query = params ? `?${new URLSearchParams(params).toString()}` : \"\";\n        return apiRequest(`/catalogue/services/${query}`);\n    },\n    getServiceDetail: (slug)=>apiRequest(`/catalogue/services/${slug}/`),\n    searchServices: (params)=>{\n        const query = new URLSearchParams(params).toString();\n        return apiRequest(`/catalogue/services/search/?${query}`);\n    }\n};\nconst cartApi = {\n    // Cart operations\n    getCart: ()=>apiRequest(\"/cart/\"),\n    getCartSummary: ()=>apiRequest(\"/cart/summary/\"),\n    addToCart: (service, quantity)=>apiRequest(\"/cart/add/\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                service,\n                quantity\n            })\n        }),\n    updateCartItem: (itemId, quantity)=>apiRequest(`/cart/items/${itemId}/update/`, {\n            method: \"PUT\",\n            body: JSON.stringify({\n                quantity\n            })\n        }),\n    removeCartItem: (itemId)=>apiRequest(`/cart/items/${itemId}/remove/`, {\n            method: \"DELETE\"\n        }),\n    clearCart: ()=>apiRequest(\"/cart/clear/\", {\n            method: \"POST\"\n        }),\n    // Coupon operations\n    applyCoupon: (coupon_code)=>apiRequest(\"/cart/coupon/apply/\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                coupon_code\n            })\n        }),\n    removeCoupon: ()=>apiRequest(\"/cart/coupon/remove/\", {\n            method: \"POST\"\n        })\n};\nconst orderApi = {\n    // Orders\n    getOrders: ()=>apiRequest(\"/orders/\"),\n    getOrderDetail: (orderNumber)=>apiRequest(`/orders/${orderNumber}/`),\n    createOrder: (data)=>apiRequest(\"/orders/\", {\n            method: \"POST\",\n            body: JSON.stringify(data)\n        }),\n    cancelOrder: (orderNumber, cancellation_reason)=>apiRequest(`/orders/${orderNumber}/cancel/`, {\n            method: \"POST\",\n            body: JSON.stringify({\n                cancellation_reason\n            })\n        })\n};\nconst paymentApi = {\n    // Payments\n    initiatePayment: (data)=>apiRequest(\"/payments/initiate/\", {\n            method: \"POST\",\n            body: JSON.stringify(data)\n        }),\n    handleRazorpayCallback: (data)=>apiRequest(\"/payments/razorpay/callback/\", {\n            method: \"POST\",\n            body: JSON.stringify(data)\n        }),\n    getPaymentStatus: (transactionId)=>apiRequest(`/payments/status/${transactionId}/`)\n};\nconst couponApi = {\n    validateCoupon: (coupon_code, cart_amount)=>apiRequest(\"/coupons/validate/\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                coupon_code,\n                cart_amount\n            })\n        })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/errorUtils.ts":
/*!*******************************!*\
  !*** ./src/lib/errorUtils.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getUserFriendlyErrorMessage: () => (/* binding */ getUserFriendlyErrorMessage),\n/* harmony export */   isAlreadyRegisteredError: () => (/* binding */ isAlreadyRegisteredError),\n/* harmony export */   isInvalidOTPError: () => (/* binding */ isInvalidOTPError),\n/* harmony export */   isUnregisteredMobileError: () => (/* binding */ isUnregisteredMobileError)\n/* harmony export */ });\n/**\n * Check if an API error indicates an unregistered mobile number\n */ const isUnregisteredMobileError = (error)=>{\n    if (error.status !== 400) return false;\n    const message = error.message?.toLowerCase() || \"\";\n    const detailMessage = error.details?.detail ? (Array.isArray(error.details.detail) ? error.details.detail.join(\" \") : String(error.details.detail)).toLowerCase() : \"\";\n    const errorMessage = error.details?.error ? (Array.isArray(error.details.error) ? error.details.error.join(\" \") : String(error.details.error)).toLowerCase() : \"\";\n    const unregisteredKeywords = [\n        \"not registered\",\n        \"unregistered\",\n        \"mobile number not found\",\n        \"user not found\",\n        \"does not exist\"\n    ];\n    return unregisteredKeywords.some((keyword)=>message.includes(keyword) || detailMessage.includes(keyword) || errorMessage.includes(keyword));\n};\n/**\n * Check if an API error indicates an already registered mobile number\n */ const isAlreadyRegisteredError = (error)=>{\n    if (error.status !== 400) return false;\n    const message = error.message?.toLowerCase() || \"\";\n    const detailMessage = error.details?.detail ? (Array.isArray(error.details.detail) ? error.details.detail.join(\" \") : String(error.details.detail)).toLowerCase() : \"\";\n    const errorMessage = error.details?.error ? (Array.isArray(error.details.error) ? error.details.error.join(\" \") : String(error.details.error)).toLowerCase() : \"\";\n    const registeredKeywords = [\n        \"already registered\",\n        \"already exists\",\n        \"mobile number already in use\",\n        \"user already exists\",\n        \"duplicate\"\n    ];\n    return registeredKeywords.some((keyword)=>message.includes(keyword) || detailMessage.includes(keyword) || errorMessage.includes(keyword));\n};\n/**\n * Check if an API error indicates invalid OTP\n */ const isInvalidOTPError = (error)=>{\n    if (error.status !== 400) return false;\n    const message = error.message?.toLowerCase() || \"\";\n    const detailMessage = error.details?.detail ? (Array.isArray(error.details.detail) ? error.details.detail.join(\" \") : String(error.details.detail)).toLowerCase() : \"\";\n    const errorMessage = error.details?.error ? (Array.isArray(error.details.error) ? error.details.error.join(\" \") : String(error.details.error)).toLowerCase() : \"\";\n    const otpKeywords = [\n        \"invalid otp\",\n        \"incorrect otp\",\n        \"otp expired\",\n        \"otp not found\",\n        \"wrong otp\"\n    ];\n    return otpKeywords.some((keyword)=>message.includes(keyword) || detailMessage.includes(keyword) || errorMessage.includes(keyword));\n};\n/**\n * Get a user-friendly error message from an API error\n */ const getUserFriendlyErrorMessage = (error)=>{\n    if (isUnregisteredMobileError(error)) {\n        return \"This mobile number is not registered. Please sign up first.\";\n    }\n    if (isAlreadyRegisteredError(error)) {\n        return \"This mobile number is already registered. Please login instead.\";\n    }\n    if (isInvalidOTPError(error)) {\n        return \"Invalid or expired OTP. Please try again.\";\n    }\n    // Return the original error message or a generic one\n    return error.message || \"An unexpected error occurred. Please try again.\";\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/errorUtils.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"fc3cb988b43e\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaG9tZS1zZXJ2aWNlcy1jdXN0b21lci8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/OGVkZSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImZjM2NiOTg4YjQzZVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/auth/login/page.tsx":
/*!*************************************!*\
  !*** ./src/app/auth/login/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\next_js_customer\src\app\auth\login\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/error.tsx":
/*!***************************!*\
  !*** ./src/app/error.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\next_js_customer\src\app\error.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_CartContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/CartContext */ \"(rsc)/./src/contexts/CartContext.tsx\");\n/* harmony import */ var _components_ui_Toaster__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Toaster */ \"(rsc)/./src/components/ui/Toaster.tsx\");\n\n\n\n\n\n\n// metadataBase is now set inside the metadata object\n// Define viewport separately (Next.js 13+ requirement)\nconst viewport = {\n    width: \"device-width\",\n    initialScale: 1,\n    maximumScale: 1,\n    themeColor: \"#3b82f6\"\n};\nconst metadata = {\n    metadataBase: new URL(\"http://localhost:3000\" || 0),\n    title: \"Home Services - Professional Services at Your Doorstep\",\n    description: \"Book professional home services including cleaning, plumbing, electrical work, and more. Fast, reliable, and affordable services.\",\n    keywords: \"home services, cleaning, plumbing, electrical, repair, maintenance, professional services\",\n    authors: [\n        {\n            name: \"Home Services Team\"\n        }\n    ],\n    creator: \"Home Services\",\n    publisher: \"Home Services\",\n    formatDetection: {\n        email: false,\n        address: false,\n        telephone: false\n    },\n    manifest: \"/manifest.json\",\n    appleWebApp: {\n        capable: true,\n        statusBarStyle: \"default\",\n        title: \"Home Services\"\n    },\n    openGraph: {\n        type: \"website\",\n        siteName: \"Home Services\",\n        title: \"Home Services - Professional Services at Your Doorstep\",\n        description: \"Book professional home services including cleaning, plumbing, electrical work, and more.\",\n        images: [\n            {\n                url: \"/og-image.jpg\",\n                width: 1200,\n                height: 630,\n                alt: \"Home Services\"\n            }\n        ]\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"Home Services - Professional Services at Your Doorstep\",\n        description: \"Book professional home services including cleaning, plumbing, electrical work, and more.\",\n        images: [\n            \"/og-image.jpg\"\n        ]\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/icons/icon-192x192.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-status-bar-style\",\n                        content: \"default\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-title\",\n                        content: \"Home Services\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-TileColor\",\n                        content: \"#3b82f6\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-tap-highlight\",\n                        content: \"no\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().className),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Toaster__WEBPACK_IMPORTED_MODULE_4__.ToastProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_CartContext__WEBPACK_IMPORTED_MODULE_3__.CartProvider, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"min-h-screen bg-gray-50\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\vinay\\\\Projects\\\\Home_services\\\\next_js_customer\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/loading.tsx":
/*!*****************************!*\
  !*** ./src/app/loading.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\next_js_customer\src\app\loading.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/not-found.tsx":
/*!*******************************!*\
  !*** ./src/app/not-found.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\next_js_customer\src\app\not-found.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/components/ui/Toaster.tsx":
/*!***************************************!*\
  !*** ./src/components/ui/Toaster.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ToastProvider: () => (/* binding */ e1),
/* harmony export */   Toaster: () => (/* binding */ e2),
/* harmony export */   useToast: () => (/* binding */ e0),
/* harmony export */   useToastNotification: () => (/* binding */ e3)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\next_js_customer\src\components\ui\Toaster.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\next_js_customer\src\components\ui\Toaster.tsx#useToast`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\next_js_customer\src\components\ui\Toaster.tsx#ToastProvider`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\next_js_customer\src\components\ui\Toaster.tsx#Toaster`);

const e3 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\next_js_customer\src\components\ui\Toaster.tsx#useToastNotification`);


/***/ }),

/***/ "(rsc)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e1),
/* harmony export */   useAuth: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\next_js_customer\src\contexts\AuthContext.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\next_js_customer\src\contexts\AuthContext.tsx#useAuth`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\next_js_customer\src\contexts\AuthContext.tsx#AuthProvider`);


/***/ }),

/***/ "(rsc)/./src/contexts/CartContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/CartContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CartProvider: () => (/* binding */ e1),
/* harmony export */   useCart: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\next_js_customer\src\contexts\CartContext.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\next_js_customer\src\contexts\CartContext.tsx#useCart`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\next_js_customer\src\contexts\CartContext.tsx#CartProvider`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/lucide-react","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Flogin%2Fpage&page=%2Fauth%2Flogin%2Fpage&appPaths=%2Fauth%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fauth%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdodob%5COneDrive%5CDocuments%5Cvinay%5CProjects%5CHome_services%5Cnext_js_customer&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();