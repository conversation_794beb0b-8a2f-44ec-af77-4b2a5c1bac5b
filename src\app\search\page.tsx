'use client';

import React, { useEffect, useState } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import { Search, Filter, ShoppingCart, Clock, Star } from 'lucide-react';
import { MainLayout } from '@/components/layout/MainLayout';
import { LoadingPage, LoadingButton } from '@/components/ui/LoadingSpinner';
import { useCart } from '@/contexts/CartContext';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/components/ui/Toaster';
import { catalogueApi } from '@/lib/api';
import { Service, Category } from '@/types/api';

export default function SearchPage() {
  const [services, setServices] = useState<Service[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [addingToCart, setAddingToCart] = useState<number | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [priceRange, setPriceRange] = useState({ min: '', max: '' });
  const [showFilters, setShowFilters] = useState(false);

  const searchParams = useSearchParams();
  const router = useRouter();
  const { addToCart } = useCart();
  const { isAuthenticated } = useAuth();
  const { showToast } = useToast();

  const query = searchParams.get('q') || '';

  useEffect(() => {
    setSearchQuery(query);
    fetchData();
  }, [query]);

  const fetchData = async () => {
    setIsLoading(true);
    try {
      const [servicesData, categoriesData] = await Promise.all([
        catalogueApi.searchServices({ q: query }),
        catalogueApi.getCategories({ level: 0 }),
      ]);

      console.log('Search services data:', servicesData);
      console.log('Search categories data:', categoriesData);

      // Handle services data
      if (Array.isArray(servicesData)) {
        setServices(servicesData as Service[]);
      } else {
        console.warn('Search services data is not an array:', servicesData);
        setServices([]);
      }

      // Handle categories data
      if (Array.isArray(categoriesData)) {
        setCategories(categoriesData as Category[]);
      } else {
        console.warn('Search categories data is not an array:', categoriesData);
        setCategories([]);
      }
    } catch (error) {
      console.error('Failed to fetch search results:', error);
      showToast({ type: 'error', title: 'Failed to load search results' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      router.push(`/search?q=${encodeURIComponent(searchQuery.trim())}`);
    }
  };

  const handleFilter = async () => {
    setIsLoading(true);
    try {
      const params: any = { q: query };
      if (selectedCategory) params.category = selectedCategory;
      if (priceRange.min) params.min_price = priceRange.min;
      if (priceRange.max) params.max_price = priceRange.max;

      const servicesData = await catalogueApi.searchServices(params);
      setServices(servicesData as Service[]);
    } catch (error) {
      console.error('Failed to apply filters:', error);
      showToast({ type: 'error', title: 'Failed to apply filters' });
    } finally {
      setIsLoading(false);
    }
  };

  const clearFilters = () => {
    setSelectedCategory('');
    setPriceRange({ min: '', max: '' });
    fetchData();
  };

  const handleAddToCart = async (service: Service) => {
    if (!isAuthenticated) {
      router.push(`/auth/login?redirect=${encodeURIComponent(window.location.pathname)}`);
      return;
    }

    setAddingToCart(service.id);
    try {
      await addToCart(service);
      showToast({ type: 'success', title: 'Added to cart', message: service.title });
    } catch (error: any) {
      showToast({ type: 'error', title: 'Failed to add to cart', message: error.message });
    } finally {
      setAddingToCart(null);
    }
  };

  if (isLoading) {
    return (
      <MainLayout>
        <LoadingPage message="Searching services..." />
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search Header */}
        <div className="mb-8">
          <form onSubmit={handleSearch} className="max-w-2xl">
            <div className="relative">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search for services..."
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
              <Search className="absolute left-3 top-3.5 h-5 w-5 text-gray-400" />
            </div>
          </form>
          
          {query && (
            <div className="mt-4">
              <h1 className="text-2xl font-bold text-gray-900">
                Search results for "{query}"
              </h1>
              <p className="text-gray-600 mt-1">
                {services.length} service{services.length !== 1 ? 's' : ''} found
              </p>
            </div>
          )}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Filters Sidebar */}
          <div className="lg:col-span-1">
            <div className="card p-6 sticky top-24">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Filters</h3>
                <button
                  onClick={() => setShowFilters(!showFilters)}
                  className="lg:hidden btn-outline"
                >
                  <Filter className="h-4 w-4" />
                </button>
              </div>

              <div className={`space-y-6 ${showFilters ? 'block' : 'hidden lg:block'}`}>
                {/* Category Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Category
                  </label>
                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="input"
                  >
                    <option value="">All Categories</option>
                    {categories && Array.isArray(categories) && categories.map((category) => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Price Range Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Price Range
                  </label>
                  <div className="grid grid-cols-2 gap-2">
                    <input
                      type="number"
                      value={priceRange.min}
                      onChange={(e) => setPriceRange({ ...priceRange, min: e.target.value })}
                      placeholder="Min"
                      className="input"
                    />
                    <input
                      type="number"
                      value={priceRange.max}
                      onChange={(e) => setPriceRange({ ...priceRange, max: e.target.value })}
                      placeholder="Max"
                      className="input"
                    />
                  </div>
                </div>

                {/* Filter Actions */}
                <div className="space-y-2">
                  <button
                    onClick={handleFilter}
                    className="w-full btn-primary"
                  >
                    Apply Filters
                  </button>
                  <button
                    onClick={clearFilters}
                    className="w-full btn-secondary"
                  >
                    Clear Filters
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Search Results */}
          <div className="lg:col-span-3">
            {services.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                {services.map((service) => (
                  <div key={service.id} className="card-hover overflow-hidden">
                    {/* Service Image */}
                    <div className="h-48 bg-gray-200 relative">
                      {service.image ? (
                        <Image
                          src={service.image}
                          alt={service.title}
                          fill
                          className="object-cover"
                        />
                      ) : (
                        <div className="w-full h-full bg-primary-100 flex items-center justify-center">
                          <span className="text-primary-600 font-bold text-2xl">
                            {service.title.charAt(0)}
                          </span>
                        </div>
                      )}
                      {service.discount_percentage && service.discount_percentage > 0 && (
                        <div className="absolute top-2 right-2 bg-red-500 text-white px-2 py-1 rounded text-sm font-medium">
                          {service.discount_percentage}% OFF
                        </div>
                      )}
                    </div>

                    {/* Service Details */}
                    <div className="p-6">
                      <div className="mb-2">
                        <span className="text-sm text-primary-600 font-medium">
                          {service.category_name}
                        </span>
                      </div>
                      
                      <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                        {service.title}
                      </h3>
                      
                      <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                        {service.description}
                      </p>

                      {/* Service Info */}
                      <div className="flex items-center space-x-4 mb-4 text-sm text-gray-500">
                        <div className="flex items-center">
                          <Clock className="h-4 w-4 mr-1" />
                          {service.time_to_complete}
                        </div>
                        <div className="flex items-center">
                          <Star className="h-4 w-4 mr-1 text-yellow-400" />
                          4.8
                        </div>
                      </div>

                      {/* Pricing */}
                      <div className="flex items-center justify-between mb-4">
                        <div>
                          <div className="flex items-center space-x-2">
                            <span className="text-2xl font-bold text-gray-900">
                              ₹{service.current_price}
                            </span>
                            {service.discount_price && service.base_price !== service.current_price && (
                              <span className="text-lg text-gray-500 line-through">
                                ₹{service.base_price}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Actions */}
                      <div className="flex space-x-2">
                        <Link
                          href={`/services/${service.slug}`}
                          className="flex-1 btn-outline text-center"
                        >
                          View Details
                        </Link>
                        <LoadingButton
                          onClick={() => handleAddToCart(service)}
                          isLoading={addingToCart === service.id}
                          className="flex-1 btn-primary"
                        >
                          <ShoppingCart className="h-4 w-4 mr-2" />
                          Add to Cart
                        </LoadingButton>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-16">
                <Search className="h-24 w-24 text-gray-300 mx-auto mb-6" />
                <h2 className="text-2xl font-bold text-gray-900 mb-4">No services found</h2>
                <p className="text-gray-600 mb-8">
                  {query 
                    ? `No services match your search for "${query}". Try different keywords or browse our categories.`
                    : 'Start searching for services to see results here.'
                  }
                </p>
                <Link href="/" className="btn-primary">
                  Browse All Services
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
