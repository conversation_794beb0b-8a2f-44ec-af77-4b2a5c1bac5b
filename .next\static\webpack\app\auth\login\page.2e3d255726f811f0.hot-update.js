"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/login/page",{

/***/ "(app-pages-browser)/./src/lib/errorUtils.ts":
/*!*******************************!*\
  !*** ./src/lib/errorUtils.ts ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getUserFriendlyErrorMessage: function() { return /* binding */ getUserFriendlyErrorMessage; },\n/* harmony export */   isAlreadyRegisteredError: function() { return /* binding */ isAlreadyRegisteredError; },\n/* harmony export */   isInvalidOTPError: function() { return /* binding */ isInvalidOTPError; },\n/* harmony export */   isUnregisteredMobileError: function() { return /* binding */ isUnregisteredMobileError; }\n/* harmony export */ });\n/**\n * Check if an API error indicates an unregistered mobile number\n */ const isUnregisteredMobileError = (error)=>{\n    var _error_message, _error_details, _error_details1;\n    if (error.status !== 400) return false;\n    const message = ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.toLowerCase()) || \"\";\n    const detailMessage = ((_error_details = error.details) === null || _error_details === void 0 ? void 0 : _error_details.detail) ? (Array.isArray(error.details.detail) ? error.details.detail.join(\" \") : String(error.details.detail)).toLowerCase() : \"\";\n    const errorMessage = ((_error_details1 = error.details) === null || _error_details1 === void 0 ? void 0 : _error_details1.error) ? (Array.isArray(error.details.error) ? error.details.error.join(\" \") : String(error.details.error)).toLowerCase() : \"\";\n    const unregisteredKeywords = [\n        \"not registered\",\n        \"unregistered\",\n        \"mobile number not found\",\n        \"user not found\",\n        \"does not exist\",\n        \"no account found\",\n        \"please register\"\n    ];\n    return unregisteredKeywords.some((keyword)=>message.includes(keyword) || detailMessage.includes(keyword) || errorMessage.includes(keyword));\n};\n/**\n * Check if an API error indicates an already registered mobile number\n */ const isAlreadyRegisteredError = (error)=>{\n    var _error_message, _error_details, _error_details1;\n    if (error.status !== 400) return false;\n    const message = ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.toLowerCase()) || \"\";\n    const detailMessage = ((_error_details = error.details) === null || _error_details === void 0 ? void 0 : _error_details.detail) ? (Array.isArray(error.details.detail) ? error.details.detail.join(\" \") : String(error.details.detail)).toLowerCase() : \"\";\n    const errorMessage = ((_error_details1 = error.details) === null || _error_details1 === void 0 ? void 0 : _error_details1.error) ? (Array.isArray(error.details.error) ? error.details.error.join(\" \") : String(error.details.error)).toLowerCase() : \"\";\n    const registeredKeywords = [\n        \"already registered\",\n        \"already exists\",\n        \"mobile number already in use\",\n        \"user already exists\",\n        \"duplicate\"\n    ];\n    return registeredKeywords.some((keyword)=>message.includes(keyword) || detailMessage.includes(keyword) || errorMessage.includes(keyword));\n};\n/**\n * Check if an API error indicates invalid OTP\n */ const isInvalidOTPError = (error)=>{\n    var _error_message, _error_details, _error_details1;\n    if (error.status !== 400) return false;\n    const message = ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.toLowerCase()) || \"\";\n    const detailMessage = ((_error_details = error.details) === null || _error_details === void 0 ? void 0 : _error_details.detail) ? (Array.isArray(error.details.detail) ? error.details.detail.join(\" \") : String(error.details.detail)).toLowerCase() : \"\";\n    const errorMessage = ((_error_details1 = error.details) === null || _error_details1 === void 0 ? void 0 : _error_details1.error) ? (Array.isArray(error.details.error) ? error.details.error.join(\" \") : String(error.details.error)).toLowerCase() : \"\";\n    const otpKeywords = [\n        \"invalid otp\",\n        \"incorrect otp\",\n        \"otp expired\",\n        \"otp not found\",\n        \"wrong otp\"\n    ];\n    return otpKeywords.some((keyword)=>message.includes(keyword) || detailMessage.includes(keyword) || errorMessage.includes(keyword));\n};\n/**\n * Get a user-friendly error message from an API error\n */ const getUserFriendlyErrorMessage = (error)=>{\n    if (isUnregisteredMobileError(error)) {\n        return \"This mobile number is not registered. Please sign up first.\";\n    }\n    if (isAlreadyRegisteredError(error)) {\n        return \"This mobile number is already registered. Please login instead.\";\n    }\n    if (isInvalidOTPError(error)) {\n        return \"Invalid or expired OTP. Please try again.\";\n    }\n    // Return the original error message or a generic one\n    return error.message || \"An unexpected error occurred. Please try again.\";\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/errorUtils.ts\n"));

/***/ })

});